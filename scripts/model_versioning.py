#!/usr/bin/env python3
"""
Model Versioning Script - Demonstration of MLOps model versioning capabilities.

This script demonstrates how to use the MLOps infrastructure for model versioning,
experiment tracking, and model registry management in the Blender AI Agent system.
"""

import argparse
import json
import sys
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent))

from mlops import (
    ModelRegistry, ModelType, ModelStatus,
    ExperimentTracker,
    ModelVersionManager,
    LocalStorageBackend, ModelArtifactManager
)


def demo_model_registry():
    """Demonstrate model registry functionality."""
    print("=== Model Registry Demo ===")
    
    registry = ModelRegistry()
    
    # Register different types of models
    models = [
        {
            'name': 'image_analysis_cv_model',
            'model_type': ModelType.COMPUTER_VISION,
            'version': '1.0.0',
            'description': 'ResNet50-based image analysis model for shape detection',
            'framework': 'pytorch',
            'architecture': 'resnet50',
            'training_dataset': 'custom_shapes_dataset_v1',
            'performance_metrics': {'accuracy': 0.92, 'f1_score': 0.89}
        },
        {
            'name': 'code_generation_llm',
            'model_type': ModelType.LANGUAGE_MODEL,
            'version': '1.0.0',
            'description': 'GPT-4 configuration for Blender Python code generation',
            'framework': 'openai_api',
            'architecture': 'gpt-4',
            'training_parameters': {
                'temperature': 0.1,
                'max_tokens': 2048,
                'top_p': 0.95
            }
        },
        {
            'name': 'knowledge_agent_rl_policy',
            'model_type': ModelType.REINFORCEMENT_LEARNING,
            'version': '1.0.0',
            'description': 'PPO policy for knowledge agent tool selection',
            'framework': 'ray_rllib',
            'architecture': 'ppo',
            'training_parameters': {
                'learning_rate': 0.0003,
                'gamma': 0.99,
                'lambda': 0.95
            },
            'performance_metrics': {'avg_reward': 0.75, 'success_rate': 0.82}
        }
    ]
    
    model_ids = []
    for model_info in models:
        model_id = registry.register_model(**model_info)
        model_ids.append(model_id)
        print(f"Registered {model_info['name']} with ID: {model_id}")
    
    # Demonstrate model queries
    print("\n--- Listing CV Models ---")
    cv_models = registry.list_models(model_type=ModelType.COMPUTER_VISION)
    for model in cv_models:
        print(f"- {model.name} v{model.version}: {model.description}")
    
    # Update model status
    print("\n--- Promoting Model to Production ---")
    cv_model_id = model_ids[0]
    registry.update_model(cv_model_id, status=ModelStatus.PRODUCTION)
    
    updated_model = registry.get_model(cv_model_id)
    print(f"Model {updated_model.name} status: {updated_model.status.value}")
    
    return registry, model_ids


def demo_experiment_tracking():
    """Demonstrate experiment tracking functionality."""
    print("\n=== Experiment Tracking Demo ===")
    
    tracker = ExperimentTracker()
    
    # Create experiments for different model types
    experiments = [
        {
            'name': 'cv_model_training_v1',
            'description': 'Training ResNet50 for shape detection',
            'parameters': {
                'batch_size': 32,
                'learning_rate': 0.001,
                'epochs': 50,
                'optimizer': 'adam'
            },
            'model_type': 'computer_vision',
            'framework': 'pytorch',
            'dataset': 'shapes_dataset_v1'
        },
        {
            'name': 'rl_policy_training_v1',
            'description': 'Training PPO policy for knowledge agent',
            'parameters': {
                'learning_rate': 0.0003,
                'gamma': 0.99,
                'lambda': 0.95,
                'clip_param': 0.2
            },
            'model_type': 'reinforcement_learning',
            'framework': 'ray_rllib'
        }
    ]
    
    experiment_ids = []
    for exp_info in experiments:
        exp_id = tracker.create_experiment(**exp_info)
        experiment_ids.append(exp_id)
        print(f"Created experiment: {exp_info['name']} (ID: {exp_id})")
    
    # Simulate training progress
    print("\n--- Logging Training Metrics ---")
    cv_exp_id = experiment_ids[0]
    
    # Log metrics for multiple epochs
    for epoch in range(1, 6):
        metrics = {
            'epoch': epoch,
            'train_loss': 0.5 - (epoch * 0.08),
            'val_loss': 0.6 - (epoch * 0.07),
            'train_accuracy': 0.7 + (epoch * 0.04),
            'val_accuracy': 0.65 + (epoch * 0.04)
        }
        tracker.log_metrics(cv_exp_id, metrics, step=epoch)
        print(f"Epoch {epoch}: val_accuracy = {metrics['val_accuracy']:.3f}")
    
    # Log artifacts
    tracker.log_artifact(cv_exp_id, 'model_checkpoint', '/tmp/model_epoch_5.pth')
    tracker.log_artifact(cv_exp_id, 'training_plot', '/tmp/training_curves.png')
    
    # Finish experiment
    tracker.finish_experiment(cv_exp_id, status='completed')
    
    # Find best experiment
    print("\n--- Finding Best Experiment ---")
    best_exp = tracker.get_best_experiment('val_accuracy', maximize=True)
    if best_exp:
        print(f"Best experiment: {best_exp.name}")
        print(f"Best val_accuracy: {best_exp.final_metrics.get('val_accuracy', 'N/A')}")
    
    return tracker, experiment_ids


def demo_version_management():
    """Demonstrate model version management."""
    print("\n=== Model Version Management Demo ===")
    
    version_manager = ModelVersionManager()
    
    # Create initial versions
    models_to_version = [
        {
            'model_name': 'image_analysis_model',
            'version': '1.0.0',
            'description': 'Initial release of image analysis model',
            'performance_metrics': {'accuracy': 0.85, 'f1_score': 0.82}
        },
        {
            'model_name': 'image_analysis_model',
            'version': '1.1.0',
            'description': 'Improved accuracy with data augmentation',
            'performance_metrics': {'accuracy': 0.89, 'f1_score': 0.86},
            'parent_version': '1.0.0'
        },
        {
            'model_name': 'image_analysis_model',
            'version': '1.1.1',
            'description': 'Bug fix for edge case handling',
            'performance_metrics': {'accuracy': 0.89, 'f1_score': 0.87},
            'parent_version': '1.1.0'
        }
    ]
    
    for model_info in models_to_version:
        version = version_manager.create_version(**model_info)
        print(f"Created version {version.version}: {version.description}")
    
    # Demonstrate version operations
    print("\n--- Version Operations ---")
    
    # Get latest version
    latest = version_manager.get_latest_version('image_analysis_model')
    print(f"Latest version: {latest.version}")
    
    # Promote to stable
    version_manager.promote_version('image_analysis_model', '1.1.0', stable=True)
    print("Promoted v1.1.0 to stable")
    
    # Get latest stable version
    latest_stable = version_manager.get_latest_version('image_analysis_model', stable_only=True)
    print(f"Latest stable version: {latest_stable.version}")
    
    # Compare versions
    comparison = version_manager.compare_versions('image_analysis_model', '1.0.0', '1.1.1')
    print(f"\nVersion comparison:")
    print(f"- v1.0.0 accuracy: {comparison['version1']['performance_metrics']['accuracy']}")
    print(f"- v1.1.1 accuracy: {comparison['version2']['performance_metrics']['accuracy']}")
    print(f"- Compatible: {comparison['compatibility']}")
    
    # Suggest next version
    next_patch = version_manager.suggest_next_version('image_analysis_model', 'patch')
    next_minor = version_manager.suggest_next_version('image_analysis_model', 'minor')
    print(f"\nSuggested next versions:")
    print(f"- Next patch: {next_patch}")
    print(f"- Next minor: {next_minor}")
    
    return version_manager


def demo_storage_and_artifacts():
    """Demonstrate storage backend and artifact management."""
    print("\n=== Storage and Artifacts Demo ===")
    
    # Initialize storage backend
    storage = LocalStorageBackend()
    artifact_manager = ModelArtifactManager(storage)
    
    # Create some dummy files to demonstrate
    demo_dir = Path("demo_artifacts")
    demo_dir.mkdir(exist_ok=True)
    
    # Create dummy model file
    model_file = demo_dir / "model.pth"
    model_file.write_text("# Dummy PyTorch model file\nmodel_state_dict = {}")
    
    # Create dummy config file
    config_file = demo_dir / "config.json"
    config_file.write_text(json.dumps({
        "model_type": "resnet50",
        "num_classes": 10,
        "input_size": [224, 224, 3]
    }, indent=2))
    
    try:
        # Save model artifacts
        print("Saving model artifacts...")
        model_path = artifact_manager.save_model_artifact(
            "image_classifier", "1.0.0", "model.pth", model_file
        )
        config_path = artifact_manager.save_model_artifact(
            "image_classifier", "1.0.0", "config.json", config_file
        )
        
        print(f"Saved model to: {model_path}")
        print(f"Saved config to: {config_path}")
        
        # List artifacts
        artifacts = artifact_manager.list_model_artifacts("image_classifier", "1.0.0")
        print(f"Model artifacts: {artifacts}")
        
        # Get storage stats
        stats = storage.get_storage_stats()
        print(f"\nStorage stats:")
        print(f"- Total size: {stats['total_size_mb']} MB")
        print(f"- File count: {stats['file_count']}")
        
    finally:
        # Cleanup
        import shutil
        if demo_dir.exists():
            shutil.rmtree(demo_dir)


def main():
    """Main demonstration function."""
    parser = argparse.ArgumentParser(description='MLOps Model Versioning Demo')
    parser.add_argument('--demo', choices=['all', 'registry', 'experiments', 'versions', 'storage'],
                       default='all', help='Which demo to run')
    
    args = parser.parse_args()
    
    print("Blender AI Agent - MLOps Model Versioning Demo")
    print("=" * 50)
    
    if args.demo in ['all', 'registry']:
        demo_model_registry()
    
    if args.demo in ['all', 'experiments']:
        demo_experiment_tracking()
    
    if args.demo in ['all', 'versions']:
        demo_version_management()
    
    if args.demo in ['all', 'storage']:
        demo_storage_and_artifacts()
    
    print("\n" + "=" * 50)
    print("Demo completed successfully!")
    print("\nMLOps data has been saved to:")
    print("- mlops_data/model_registry.json")
    print("- mlops_data/experiments/")
    print("- mlops_data/models/")
    print("- mlops_data/storage/")


if __name__ == '__main__':
    main()

"""
Knowledge Agent for Blender 3D Model Generation AI Agent System

This module implements a knowledge retrieval agent that provides access to Blender Python API
documentation, MCP (Molecular Nodes) usage examples, and 3D modeling best practices through
a vector database-powered RAG (Retrieval-Augmented Generation) system.

Enhanced with Ray RLlib integration for optimal tool selection and decision making.

Author: Augment Agent
Date: 2025-07-17
"""

import os
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import chromadb
from chromadb.config import Settings
import openai
from openai import OpenAI

# Ray RLlib imports (optional)
try:
    import ray
    import numpy as np
    from ray.rllib.algorithms.ppo import PPOConfig
    from ray.rllib.env.env_context import EnvContext
    HAS_RAY = True
except ImportError:
    HAS_RAY = False
    print("Warning: Ray RLlib not available. Using fallback tool selection.")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeToolType(Enum):
    """Enumeration of available knowledge tools for RL selection."""
    VECTOR_SEARCH = 0
    KEYWORD_SEARCH = 1
    BLENDER_API_LOOKUP = 2
    MCP_EXAMPLES = 3
    BEST_PRACTICES = 4


@dataclass
class ToolSelectionContext:
    """Context for RL-based tool selection."""
    query_type: str
    query_complexity: float
    domain_specificity: float
    previous_tool_success: float
    time_constraint: float


class KnowledgeSource(Enum):
    """Enumeration of knowledge source types."""
    BLENDER_API = "blender_api"
    MCP_DOCS = "mcp_docs"
    BEST_PRACTICES = "best_practices"
    EXAMPLES = "examples"


@dataclass
class KnowledgeChunk:
    """Represents a chunk of knowledge with metadata."""
    id: str
    content: str
    source: KnowledgeSource
    topic: str
    subtopic: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class RetrievalResult:
    """Represents a knowledge retrieval result."""
    chunk: KnowledgeChunk
    relevance_score: float
    distance: float


class KnowledgeRetrievalError(Exception):
    """Exception raised for knowledge retrieval errors."""
    pass


class KnowledgeAgent:
    """
    Knowledge Agent for retrieving Blender API documentation and MCP examples.
    
    This agent uses ChromaDB for vector storage and OpenAI embeddings for semantic search.
    It provides context-aware knowledge retrieval to support other agents in the system.
    """
    
    def __init__(self,
                 knowledge_base_path: str = "knowledge_base",
                 db_path: str = "chroma_db",
                 openai_api_key: Optional[str] = None,
                 embedding_model: str = "text-embedding-3-small",
                 enable_rl: bool = True):
        """
        Initialize the Knowledge Agent.

        Args:
            knowledge_base_path: Path to knowledge base files
            db_path: Path to ChromaDB storage
            openai_api_key: OpenAI API key for embeddings
            embedding_model: OpenAI embedding model to use
            enable_rl: Whether to enable RL-based tool selection
        """
        self.knowledge_base_path = Path(knowledge_base_path)
        self.db_path = Path(db_path)
        self.embedding_model = embedding_model
        self.enable_rl = enable_rl and HAS_RAY

        # Initialize OpenAI client
        api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.warning("No OpenAI API key provided. Some functionality may be limited.")
            self.openai_client = None
        else:
            self.openai_client = OpenAI(api_key=api_key)

        # Initialize ChromaDB
        self.chroma_client = chromadb.PersistentClient(
            path=str(self.db_path),
            settings=Settings(anonymized_telemetry=False)
        )

        # Create or get collection
        self.collection = self.chroma_client.get_or_create_collection(
            name="blender_knowledge",
            metadata={"description": "Blender API and MCP knowledge base"}
        )

        self.knowledge_chunks: List[KnowledgeChunk] = []

        # Initialize RL components
        self._init_rl_components()

        # Tool performance tracking for reward calculation
        self.tool_performance_history = {tool: [] for tool in KnowledgeToolType}
        self.query_success_rate = 0.0

    def _init_rl_components(self):
        """Initialize RL components for tool selection."""
        if not self.enable_rl:
            logger.info("RL disabled, using heuristic tool selection")
            self.rl_policy = None
            return

        try:
            # Initialize Ray if not already done
            if not ray.is_initialized():
                ray.init(ignore_reinit_error=True)

            # Create simple RL environment for knowledge tool selection
            from rl_env.minimal_blender_task_env import MinimalBlenderTaskEnv

            # Configure PPO for knowledge tool selection
            self.rl_config = (
                PPOConfig()
                .environment(MinimalBlenderTaskEnv)
                .framework("torch")
                .training(
                    lr=0.0001,
                    num_sgd_iter=5,
                    train_batch_size=500,
                )
                .resources(num_gpus=0)
            )

            # Build algorithm (will use pre-trained if available)
            self.rl_policy = self.rl_config.build()
            logger.info("RL policy initialized for knowledge tool selection")

        except Exception as e:
            logger.warning(f"Failed to initialize RL components: {e}")
            self.enable_rl = False
            self.rl_policy = None

    def load_knowledge_base(self, force_reload: bool = False) -> bool:
        """
        Load knowledge base from files and create embeddings.
        
        Args:
            force_reload: Force reload even if collection already has data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if collection already has data
            if not force_reload and self.collection.count() > 0:
                logger.info(f"Knowledge base already loaded with {self.collection.count()} chunks")
                return True
            
            # Clear existing data if force reload
            if force_reload:
                self.chroma_client.delete_collection("blender_knowledge")
                self.collection = self.chroma_client.create_collection(
                    name="blender_knowledge",
                    metadata={"description": "Blender API and MCP knowledge base"}
                )
            
            # Load knowledge chunks from files
            self._load_knowledge_chunks()
            
            if not self.knowledge_chunks:
                logger.warning("No knowledge chunks loaded")
                return False
            
            # Generate embeddings and store in vector database
            self._store_embeddings()
            
            logger.info(f"Successfully loaded {len(self.knowledge_chunks)} knowledge chunks")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load knowledge base: {e}")
            raise KnowledgeRetrievalError(f"Knowledge base loading failed: {e}")
    
    def _load_knowledge_chunks(self):
        """Load knowledge chunks from text files."""
        self.knowledge_chunks = []
        
        # Load from blender_docs_subset.txt
        blender_docs_path = self.knowledge_base_path / "blender_docs_subset.txt"
        if blender_docs_path.exists():
            self._parse_knowledge_file(blender_docs_path, KnowledgeSource.BLENDER_API)
        else:
            logger.warning(f"Blender docs file not found: {blender_docs_path}")
        
        # Load additional knowledge files if they exist
        for source_type in KnowledgeSource:
            if source_type == KnowledgeSource.BLENDER_API:
                continue  # Already loaded above
                
            filename = f"{source_type.value}_docs.txt"
            file_path = self.knowledge_base_path / filename
            if file_path.exists():
                self._parse_knowledge_file(file_path, source_type)
    
    def _parse_knowledge_file(self, file_path: Path, source: KnowledgeSource):
        """Parse a knowledge file and extract chunks."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Split content into chunks (simple approach - can be enhanced)
            chunks = self._split_into_chunks(content)
            
            for i, chunk_content in enumerate(chunks):
                if chunk_content.strip():
                    chunk_id = f"{source.value}_{file_path.stem}_{i}"
                    topic = self._extract_topic(chunk_content)
                    
                    chunk = KnowledgeChunk(
                        id=chunk_id,
                        content=chunk_content.strip(),
                        source=source,
                        topic=topic,
                        metadata={"file": str(file_path)}
                    )
                    self.knowledge_chunks.append(chunk)
                    
        except Exception as e:
            logger.error(f"Failed to parse knowledge file {file_path}: {e}")
    
    def _split_into_chunks(self, content: str, max_chunk_size: int = 1000) -> List[str]:
        """Split content into manageable chunks."""
        # Simple chunking by paragraphs and size
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = ""

        for paragraph in paragraphs:
            # If paragraph itself is too long, split it further
            if len(paragraph) > max_chunk_size:
                # Add current chunk if it exists
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = ""

                # Split long paragraph by sentences or words
                sentences = paragraph.split('. ')
                for sentence in sentences:
                    if len(sentence) > max_chunk_size:
                        # Split by words if sentence is still too long
                        words = sentence.split()
                        temp_chunk = ""
                        for word in words:
                            if len(temp_chunk) + len(word) + 1 > max_chunk_size and temp_chunk:
                                chunks.append(temp_chunk)
                                temp_chunk = word
                            else:
                                temp_chunk += " " + word if temp_chunk else word
                        if temp_chunk:
                            chunks.append(temp_chunk)
                    else:
                        if len(current_chunk) + len(sentence) > max_chunk_size and current_chunk:
                            chunks.append(current_chunk)
                            current_chunk = sentence
                        else:
                            current_chunk += ". " + sentence if current_chunk else sentence
            else:
                # Normal paragraph processing
                if len(current_chunk) + len(paragraph) > max_chunk_size and current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = paragraph
                else:
                    current_chunk += "\n\n" + paragraph if current_chunk else paragraph

        if current_chunk:
            chunks.append(current_chunk)

        return chunks
    
    def _extract_topic(self, content: str) -> str:
        """Extract topic from content (simple heuristic)."""
        lines = content.strip().split('\n')
        first_line = lines[0].strip()
        
        # Look for common patterns
        if first_line.startswith('#'):
            return first_line.strip('#').strip()
        elif 'bpy.' in first_line:
            return first_line.split('bpy.')[1].split('(')[0] if '(' in first_line else first_line.split('bpy.')[1]
        elif len(first_line) < 100:
            return first_line
        else:
            return "general"
    
    def _store_embeddings(self):
        """Generate embeddings and store in ChromaDB."""
        if not self.openai_client:
            logger.warning("No OpenAI client available for embeddings")
            # Store without embeddings for testing
            self._store_without_embeddings()
            return
        
        batch_size = 100
        for i in range(0, len(self.knowledge_chunks), batch_size):
            batch = self.knowledge_chunks[i:i + batch_size]
            self._process_embedding_batch(batch)
    
    def _process_embedding_batch(self, batch: List[KnowledgeChunk]):
        """Process a batch of chunks for embedding."""
        try:
            # Prepare texts for embedding
            texts = [chunk.content for chunk in batch]
            
            # Generate embeddings
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=texts
            )
            
            # Prepare data for ChromaDB
            ids = [chunk.id for chunk in batch]
            embeddings = [data.embedding for data in response.data]
            documents = texts
            metadatas = [
                {
                    "source": chunk.source.value,
                    "topic": chunk.topic,
                    "subtopic": chunk.subtopic or "",
                    **(chunk.metadata or {})
                }
                for chunk in batch
            ]
            
            # Add to collection
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"Processed embedding batch of {len(batch)} chunks")
            
        except Exception as e:
            logger.error(f"Failed to process embedding batch: {e}")
            raise KnowledgeRetrievalError(f"Embedding generation failed: {e}")
    
    def _store_without_embeddings(self):
        """Store chunks without embeddings (for testing)."""
        ids = [chunk.id for chunk in self.knowledge_chunks]
        documents = [chunk.content for chunk in self.knowledge_chunks]
        metadatas = [
            {
                "source": chunk.source.value,
                "topic": chunk.topic,
                "subtopic": chunk.subtopic or "",
                **(chunk.metadata or {})
            }
            for chunk in self.knowledge_chunks
        ]
        
        self.collection.add(
            ids=ids,
            documents=documents,
            metadatas=metadatas
        )

    def _select_optimal_tool(self, context: ToolSelectionContext) -> KnowledgeToolType:
        """
        Select optimal knowledge tool using RL policy or heuristics.

        Args:
            context: Context for tool selection

        Returns:
            Selected knowledge tool type
        """
        if self.enable_rl and self.rl_policy:
            try:
                # Create state vector for RL policy
                state = np.array([
                    hash(context.query_type) % 10 / 10.0,  # Normalized query type
                    context.query_complexity,
                    context.domain_specificity,
                    context.previous_tool_success,
                    context.time_constraint
                ], dtype=np.float32)

                # Get action from RL policy
                action = self.rl_policy.compute_single_action(state)

                # Map action to tool type
                if action < len(KnowledgeToolType):
                    selected_tool = list(KnowledgeToolType)[action]
                    logger.debug(f"RL selected tool: {selected_tool.name}")
                    return selected_tool

            except Exception as e:
                logger.warning(f"RL tool selection failed: {e}, falling back to heuristics")

        # Fallback to heuristic selection
        return self._heuristic_tool_selection(context)

    def _heuristic_tool_selection(self, context: ToolSelectionContext) -> KnowledgeToolType:
        """Fallback heuristic tool selection."""
        # Simple heuristics based on query characteristics
        if "blender" in context.query_type.lower() and "api" in context.query_type.lower():
            return KnowledgeToolType.BLENDER_API_LOOKUP
        elif "molecular" in context.query_type.lower() or "mcp" in context.query_type.lower():
            return KnowledgeToolType.MCP_EXAMPLES
        elif context.query_complexity > 0.7:
            return KnowledgeToolType.VECTOR_SEARCH
        elif context.domain_specificity < 0.3:
            return KnowledgeToolType.BEST_PRACTICES
        else:
            return KnowledgeToolType.KEYWORD_SEARCH

    def _calculate_tool_reward(self, tool: KnowledgeToolType,
                              query_success: bool,
                              relevance_score: float,
                              response_time: float) -> float:
        """
        Calculate reward for tool selection to update RL policy.

        Args:
            tool: Tool that was used
            query_success: Whether query was successful
            relevance_score: Average relevance score of results
            response_time: Time taken to respond

        Returns:
            Calculated reward value
        """
        # Base reward for success/failure
        base_reward = 5.0 if query_success else -2.0

        # Quality bonus based on relevance
        quality_bonus = relevance_score * 3.0

        # Efficiency bonus (faster is better)
        efficiency_bonus = max(0, 2.0 - response_time)

        # Tool-specific bonuses
        tool_bonus = 0.0
        if tool == KnowledgeToolType.VECTOR_SEARCH and relevance_score > 0.8:
            tool_bonus = 1.0
        elif tool == KnowledgeToolType.BLENDER_API_LOOKUP and "blender" in str(query_success):
            tool_bonus = 1.5

        total_reward = base_reward + quality_bonus + efficiency_bonus + tool_bonus

        # Update performance history
        self.tool_performance_history[tool].append(total_reward)
        if len(self.tool_performance_history[tool]) > 100:
            self.tool_performance_history[tool].pop(0)  # Keep last 100 records

        return total_reward

    def query_knowledge(self,
                       query: str,
                       top_k: int = 5,
                       source_filter: Optional[KnowledgeSource] = None,
                       enable_rl_selection: bool = True) -> List[RetrievalResult]:
        """
        Query the knowledge base for relevant information with RL-enhanced tool selection.

        Args:
            query: Search query
            top_k: Number of top results to return
            source_filter: Filter by knowledge source type
            enable_rl_selection: Whether to use RL for tool selection

        Returns:
            List of retrieval results sorted by relevance
        """
        start_time = time.time()

        try:
            if self.collection.count() == 0:
                logger.warning("Knowledge base is empty. Load knowledge base first.")
                return []

            # Create context for RL tool selection
            if enable_rl_selection and self.enable_rl:
                context = ToolSelectionContext(
                    query_type=query,
                    query_complexity=min(1.0, len(query.split()) / 20.0),
                    domain_specificity=1.0 if any(term in query.lower()
                                                for term in ['blender', 'bpy', 'molecular', 'mcp']) else 0.5,
                    previous_tool_success=self.query_success_rate,
                    time_constraint=0.5  # Default medium urgency
                )

                selected_tool = self._select_optimal_tool(context)
                logger.debug(f"Selected tool for query '{query[:50]}...': {selected_tool.name}")
            else:
                selected_tool = KnowledgeToolType.VECTOR_SEARCH  # Default

            # Execute query based on selected tool
            results = self._execute_tool_query(query, top_k, source_filter, selected_tool)

            # Calculate performance metrics
            response_time = time.time() - start_time
            avg_relevance = sum(r.relevance_score for r in results) / len(results) if results else 0.0
            query_success = len(results) > 0 and avg_relevance > 0.3

            # Update RL policy with reward
            if enable_rl_selection and self.enable_rl:
                reward = self._calculate_tool_reward(selected_tool, query_success, avg_relevance, response_time)
                logger.debug(f"Tool {selected_tool.name} reward: {reward:.2f}")

            # Update success rate
            self.query_success_rate = 0.9 * self.query_success_rate + 0.1 * (1.0 if query_success else 0.0)

            return results

        except Exception as e:
            logger.error(f"Knowledge query failed: {e}")
            return []

    def _execute_tool_query(self, query: str, top_k: int,
                           source_filter: Optional[KnowledgeSource],
                           tool: KnowledgeToolType) -> List[RetrievalResult]:
        """Execute query using the selected tool."""

        # Prepare query filters
        where_filter = {}
        if source_filter:
            where_filter["source"] = source_filter.value

        # Tool-specific query execution
        if tool == KnowledgeToolType.VECTOR_SEARCH and self.openai_client:
            # Generate query embedding
            query_embedding = self._generate_query_embedding(query)

            # Perform vector search
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=where_filter if where_filter else None
            )
        elif tool == KnowledgeToolType.BLENDER_API_LOOKUP:
            # Enhanced query for Blender API
            enhanced_query = f"blender python bpy {query}"
            if self.openai_client:
                query_embedding = self._generate_query_embedding(enhanced_query)
                results = self.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=top_k,
                    where={"source": "BLENDER_DOCS"} if not where_filter else where_filter
                )
            else:
                results = self.collection.query(
                    query_texts=[enhanced_query],
                    n_results=top_k,
                    where={"source": "BLENDER_DOCS"} if not where_filter else where_filter
                )
        elif tool == KnowledgeToolType.MCP_EXAMPLES:
            # MCP-specific search
            mcp_query = f"molecular nodes {query}"
            if self.openai_client:
                query_embedding = self._generate_query_embedding(mcp_query)
                results = self.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=top_k,
                    where={"source": "MCP_DOCS"} if not where_filter else where_filter
                )
            else:
                results = self.collection.query(
                    query_texts=[mcp_query],
                    n_results=top_k,
                    where={"source": "MCP_DOCS"} if not where_filter else where_filter
                )
        else:
            # Default keyword/text search
            results = self.collection.query(
                query_texts=[query],
                n_results=top_k,
                where=where_filter if where_filter else None
            )

        # Convert to RetrievalResult objects
        retrieval_results = []
        if results and 'ids' in results and results['ids'][0]:
            for i in range(len(results['ids'][0])):
                chunk = KnowledgeChunk(
                    id=results['ids'][0][i],
                    content=results['documents'][0][i],
                    source=KnowledgeSource(results['metadatas'][0][i]['source']),
                    topic=results['metadatas'][0][i]['topic'],
                    subtopic=results['metadatas'][0][i].get('subtopic'),
                    metadata=results['metadatas'][0][i]
                )

                distance = results['distances'][0][i] if 'distances' in results else 0.0
                relevance_score = max(0.0, 1.0 - distance)  # Convert distance to relevance

                retrieval_results.append(RetrievalResult(
                    chunk=chunk,
                    relevance_score=relevance_score,
                    distance=distance
                ))

        logger.debug(f"Tool {tool.name} retrieved {len(retrieval_results)} results")
        return retrieval_results

    def _generate_query_embedding(self, query: str) -> List[float]:
        """Generate embedding for query text."""
        try:
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=query
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to generate query embedding: {e}")
            raise KnowledgeRetrievalError(f"Query embedding failed: {e}")

    def get_blender_api_docs(self, function_name: str) -> List[RetrievalResult]:
        """
        Get Blender API documentation for a specific function.

        Args:
            function_name: Name of the Blender function/API

        Returns:
            List of relevant documentation results
        """
        query = f"bpy.{function_name}" if not function_name.startswith('bpy.') else function_name
        return self.query_knowledge(
            query=query,
            top_k=3,
            source_filter=KnowledgeSource.BLENDER_API
        )

    def get_mcp_examples(self, model_type: str) -> List[RetrievalResult]:
        """
        Get MCP (Molecular Nodes) examples for a specific model type.

        Args:
            model_type: Type of molecular model

        Returns:
            List of relevant MCP examples
        """
        query = f"molecular nodes {model_type}"
        return self.query_knowledge(
            query=query,
            top_k=3,
            source_filter=KnowledgeSource.MCP_DOCS
        )

    def get_knowledge_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base."""
        try:
            total_chunks = self.collection.count()

            # Get source distribution
            source_stats = {}
            for source in KnowledgeSource:
                count = len(self.collection.get(where={"source": source.value})['ids'])
                source_stats[source.value] = count

            return {
                "total_chunks": total_chunks,
                "source_distribution": source_stats,
                "embedding_model": self.embedding_model,
                "has_embeddings": self.openai_client is not None
            }
        except Exception as e:
            logger.error(f"Failed to get knowledge stats: {e}")
            return {"error": str(e)}

    def evaluate_retrieval_quality(self, test_queries: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Evaluate retrieval quality using test queries.

        Args:
            test_queries: List of test queries with expected results

        Returns:
            Dictionary of evaluation metrics
        """
        if not test_queries:
            return {"error": "No test queries provided"}

        total_queries = len(test_queries)
        correct_retrievals = 0
        total_relevance_score = 0.0

        for test_query in test_queries:
            query = test_query['query']
            expected_topics = set(test_query.get('expected_topics', []))

            results = self.query_knowledge(query, top_k=5)

            if results:
                # Check if any result matches expected topics
                retrieved_topics = {result.chunk.topic for result in results}
                if expected_topics.intersection(retrieved_topics):
                    correct_retrievals += 1

                # Calculate average relevance score for top result
                total_relevance_score += results[0].relevance_score

        accuracy = correct_retrievals / total_queries if total_queries > 0 else 0.0
        avg_relevance = total_relevance_score / total_queries if total_queries > 0 else 0.0

        return {
            "accuracy": accuracy,
            "average_relevance_score": avg_relevance,
            "total_queries": total_queries,
            "correct_retrievals": correct_retrievals
        }

"""
Specification Generation Agent for Blender 3D Model Generation AI Agent

This module implements a specification generation agent that takes image analysis results
and knowledge agent context to generate JSON specifications conforming to the base_model_spec.json
schema with robust validation and error handling.

Author: Augment Agent
Date: 2025-07-17
"""

import os
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

import openai
from openai import OpenAI
from pydantic import ValidationError
import jsonschema

# Import project modules
from agents.image_analysis_agent import ImageAnalysisResult, DetectedShape, ShapeType
from agents.knowledge_agent import KnowledgeAgent, RetrievalResult
from models.specs.v1.models import (
    ModelSpecification, ModelInfo, SceneSettings, Object3D, 
    Transform, Material, Vector3, Color, GeometryTypeEnum,
    CubeGeometry, SphereGeometry, CylinderGeometry, PlaneGeometry, ConeGeometry
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SpecGenerationError(Exception):
    """Custom exception for specification generation errors."""
    pass


class ValidationLevel(Enum):
    """Validation levels for generated specifications."""
    BASIC = "basic"          # Basic structure validation
    SCHEMA = "schema"        # JSON Schema validation
    PYDANTIC = "pydantic"    # Pydantic model validation
    FULL = "full"           # All validation levels


@dataclass
class SpecGenerationConfig:
    """Configuration for specification generation."""
    default_units: str = "meters"
    default_material_type: str = "basic"
    default_scale: float = 1.0
    enable_knowledge_context: bool = True
    validation_level: ValidationLevel = ValidationLevel.FULL
    max_objects_per_spec: int = 10
    openai_model: str = "gpt-4"
    max_retries: int = 3

    # V2 specific configurations
    schema_version: str = "v2.0.0"
    enable_complex_materials: bool = True
    enable_modifiers: bool = True
    enable_lighting: bool = True
    enable_mcp_structures: bool = True
    default_complexity_level: str = "intermediate"
    enable_physics: bool = False
    enable_animation: bool = False


@dataclass
class SpecGenerationResult:
    """Result of specification generation."""
    specification: Dict[str, Any]
    validation_passed: bool
    validation_errors: List[str]
    knowledge_context_used: List[str]
    generation_time: float
    confidence_score: float
    metadata: Optional[Dict[str, Any]] = None


class SpecGenerationAgent:
    """
    Specification Generation Agent for creating 3D model specifications.
    
    This agent integrates image analysis results with knowledge agent context
    to generate JSON specifications that conform to the base_model_spec.json schema.
    """
    
    def __init__(self, 
                 config: Optional[SpecGenerationConfig] = None,
                 knowledge_agent: Optional[KnowledgeAgent] = None,
                 openai_api_key: Optional[str] = None):
        """
        Initialize the Specification Generation Agent.
        
        Args:
            config: Configuration for the agent
            knowledge_agent: Knowledge agent for context retrieval
            openai_api_key: OpenAI API key for LLM calls
        """
        self.config = config or SpecGenerationConfig()
        self.knowledge_agent = knowledge_agent
        
        # Initialize OpenAI client
        api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
        if api_key:
            self.openai_client = OpenAI(api_key=api_key)
        else:
            logger.warning("No OpenAI API key provided. LLM-based generation will not be available.")
            self.openai_client = None
        
        # Load JSON schema for validation
        self.json_schema = self._load_json_schema()
        
        logger.info("SpecGenerationAgent initialized successfully")
    
    def _load_json_schema(self) -> Dict[str, Any]:
        """Load the JSON schema for validation."""
        try:
            # Determine schema version and path
            if self.config.schema_version.startswith('v2.'):
                schema_path = Path(__file__).parent.parent / "models" / "specs" / "v2" / "model_spec_schema.json"
            else:
                schema_path = Path(__file__).parent.parent / "models" / "specs" / "v1" / "base_model_spec.json"

            with open(schema_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load JSON schema: {e}")
            return {}
    
    def generate_specification(self,
                             image_analysis_result: ImageAnalysisResult,
                             user_preferences: Optional[Dict[str, Any]] = None,
                             model_name: Optional[str] = None) -> SpecGenerationResult:
        """
        Generate a 3D model specification from image analysis results.
        
        Args:
            image_analysis_result: Results from image analysis
            user_preferences: Optional user preferences for the model
            model_name: Optional name for the generated model
            
        Returns:
            SpecGenerationResult containing the generated specification and metadata
        """
        start_time = datetime.now()
        
        try:
            # Validate input - allow empty shapes, will create default object
            if not image_analysis_result.detected_shapes:
                logger.info("No shapes detected, will create default object")
            
            # Get knowledge context if available
            knowledge_context = []
            if self.config.enable_knowledge_context and self.knowledge_agent:
                knowledge_context = self._get_knowledge_context(image_analysis_result)
            
            # Generate specification using LLM or rule-based approach
            generation_method = 'rule_based'  # Default
            if self.openai_client:
                try:
                    spec_dict = self._generate_with_llm(
                        image_analysis_result,
                        knowledge_context,
                        user_preferences,
                        model_name
                    )
                    generation_method = 'llm'
                except Exception as e:
                    logger.warning(f"LLM generation failed, using rule-based: {e}")
                    spec_dict = self._generate_rule_based(
                        image_analysis_result,
                        user_preferences,
                        model_name
                    )
                    generation_method = 'rule_based'
            else:
                spec_dict = self._generate_rule_based(
                    image_analysis_result,
                    user_preferences,
                    model_name
                )
            
            # Validate the generated specification
            validation_passed, validation_errors = self._validate_specification(spec_dict)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(
                image_analysis_result, 
                spec_dict, 
                validation_passed
            )
            
            generation_time = (datetime.now() - start_time).total_seconds()
            
            return SpecGenerationResult(
                specification=spec_dict,
                validation_passed=validation_passed,
                validation_errors=validation_errors,
                knowledge_context_used=[ctx.chunk.content[:100] + "..." for ctx in knowledge_context],
                generation_time=generation_time,
                confidence_score=confidence_score,
                metadata={
                    'input_shapes_count': len(image_analysis_result.detected_shapes),
                    'overall_input_confidence': image_analysis_result.overall_confidence,
                    'generation_method': generation_method
                }
            )
            
        except Exception as e:
            logger.error(f"Specification generation failed: {e}")
            generation_time = (datetime.now() - start_time).total_seconds()
            
            return SpecGenerationResult(
                specification={},
                validation_passed=False,
                validation_errors=[str(e)],
                knowledge_context_used=[],
                generation_time=generation_time,
                confidence_score=0.0,
                metadata={'error': str(e)}
            )
    
    def _get_knowledge_context(self, image_analysis_result: ImageAnalysisResult) -> List[RetrievalResult]:
        """Get relevant knowledge context for specification generation."""
        if not self.knowledge_agent:
            return []
        
        try:
            # Build query from detected shapes
            shape_types = [shape.shape_type.value for shape in image_analysis_result.detected_shapes]
            query = f"blender python create {' '.join(shape_types)} 3d model geometry"
            
            # Query knowledge base
            results = self.knowledge_agent.query_knowledge(query, top_k=3)
            logger.info(f"Retrieved {len(results)} knowledge context items")
            return results
            
        except Exception as e:
            logger.warning(f"Failed to get knowledge context: {e}")
            return []
    
    def _generate_with_llm(self,
                          image_analysis_result: ImageAnalysisResult,
                          knowledge_context: List[RetrievalResult],
                          user_preferences: Optional[Dict[str, Any]],
                          model_name: Optional[str]) -> Dict[str, Any]:
        """Generate specification using LLM."""
        # Prepare context for LLM
        context_text = self._prepare_llm_context(
            image_analysis_result,
            knowledge_context,
            user_preferences
        )

        # Create prompt for specification generation
        prompt = self._create_specification_prompt(context_text, model_name)

        # Call LLM with retry mechanism
        response = self._call_llm_with_retry(prompt)

        # Parse and validate LLM response
        spec_dict = self._parse_llm_response(response)

        return spec_dict

    def _generate_rule_based(self,
                           image_analysis_result: ImageAnalysisResult,
                           user_preferences: Optional[Dict[str, Any]],
                           model_name: Optional[str]) -> Dict[str, Any]:
        """Generate specification using rule-based approach."""
        try:
            # Create base specification structure based on schema version
            if self.config.schema_version.startswith('v2.'):
                spec = self._create_v2_base_spec(image_analysis_result, user_preferences, model_name)
            else:
                spec = self._create_v1_base_spec(image_analysis_result, user_preferences, model_name)

            return spec

        except Exception as e:
            logger.error(f"Rule-based generation failed: {e}")
            raise SpecGenerationError(f"Failed to generate specification: {e}")

    def _create_v1_base_spec(self,
                           image_analysis_result: ImageAnalysisResult,
                           user_preferences: Optional[Dict[str, Any]],
                           model_name: Optional[str]) -> Dict[str, Any]:
        """Create v1 base specification structure."""
        spec = {
            "schema_version": "v1.0.0",
            "model_info": {
                "name": model_name or f"Generated_Model_{uuid.uuid4().hex[:8]}",
                "description": self._generate_description(image_analysis_result),
                "created_at": datetime.now().isoformat(),
                "tags": self._generate_tags(image_analysis_result)
            },
            "scene_settings": {
                "units": user_preferences.get("units", self.config.default_units) if user_preferences else self.config.default_units
            },
            "objects": []
        }

        # Convert detected shapes to 3D objects
        for i, shape in enumerate(image_analysis_result.detected_shapes):
            obj = self._shape_to_object_v1(shape, i)
            if obj:
                spec["objects"].append(obj)

        # Ensure we have at least one object
        if not spec["objects"]:
            # Create a default cube if no valid objects were generated
            spec["objects"].append(self._create_default_object())

        return spec

    def _create_v2_base_spec(self,
                           image_analysis_result: ImageAnalysisResult,
                           user_preferences: Optional[Dict[str, Any]],
                           model_name: Optional[str]) -> Dict[str, Any]:
        """Create v2 base specification structure with advanced features."""
        # Determine complexity and model type
        complexity_level = self._determine_complexity_level(image_analysis_result, user_preferences)
        model_type = self._determine_model_type(image_analysis_result, user_preferences)

        spec = {
            "schema_version": self.config.schema_version,
            "model_info": {
                "name": model_name or f"Generated_Model_{uuid.uuid4().hex[:8]}",
                "description": self._generate_description(image_analysis_result),
                "created_at": datetime.now().isoformat(),
                "tags": self._generate_tags(image_analysis_result),
                "complexity_level": complexity_level,
                "model_type": model_type
            },
            "scene_settings": {
                "units": user_preferences.get("units", self.config.default_units) if user_preferences else self.config.default_units
            },
            "objects": []
        }

        # Add optional sections based on configuration and analysis
        if self.config.enable_lighting and self._should_add_lighting(image_analysis_result):
            spec["lighting"] = self._generate_lighting_config(image_analysis_result)

        if self.config.enable_mcp_structures and model_type in ["molecular", "protein"]:
            spec["mcp_structures"] = self._generate_mcp_structures(image_analysis_result, model_type)

        # Convert detected shapes to objects with v2 features
        for i, shape in enumerate(image_analysis_result.detected_shapes):
            obj = self._shape_to_object_v2(shape, i, complexity_level)
            if obj:
                spec["objects"].append(obj)

        # Add groups if multiple objects with relationships
        if len(spec["objects"]) > 1 and self._should_create_groups(image_analysis_result):
            spec["groups"] = self._generate_object_groups(spec["objects"])

        # Ensure we have at least one object
        if not spec["objects"]:
            spec["objects"].append(self._create_default_object_v2())

        return spec

    def _shape_to_object_v1(self, shape: DetectedShape, index: int) -> Optional[Dict[str, Any]]:
        """Convert a detected shape to a 3D object specification."""
        try:
            # Generate unique ID
            obj_id = f"object_{index + 1}"

            # Map shape type to geometry
            geometry = self._create_geometry_from_shape(shape)
            if not geometry:
                return None

            # Create transform from shape properties
            transform = self._create_transform_from_shape(shape)

            # Create material from color information
            material = self._create_material_from_shape(shape)

            # Build object specification
            obj = {
                "id": obj_id,
                "name": f"{shape.shape_type.value.title()}_{index + 1}",
                "geometry": geometry,
                "visible": True
            }

            # Add optional properties if they exist
            if transform:
                obj["transform"] = transform
            if material:
                obj["material"] = material

            return obj

        except Exception as e:
            logger.warning(f"Failed to convert shape to object: {e}")
            return None

    def _create_geometry_from_shape(self, shape: DetectedShape) -> Optional[Dict[str, Any]]:
        """Create geometry specification from detected shape."""
        try:
            shape_type = shape.shape_type

            if shape_type == ShapeType.CUBE:
                return {
                    "type": "cube",
                    "size": self._estimate_size_from_shape(shape, default=2.0)
                }
            elif shape_type == ShapeType.SPHERE:
                return {
                    "type": "sphere",
                    "radius": self._estimate_size_from_shape(shape, default=1.0),
                    "subdivisions": 4
                }
            elif shape_type == ShapeType.CYLINDER:
                size = self._estimate_size_from_shape(shape, default=1.0)
                return {
                    "type": "cylinder",
                    "radius": size,
                    "height": size * 2.0,
                    "vertices": 32
                }
            elif shape_type == ShapeType.CONE:
                size = self._estimate_size_from_shape(shape, default=1.0)
                return {
                    "type": "cone",
                    "radius": size,
                    "height": size * 2.0,
                    "vertices": 32
                }
            elif shape_type == ShapeType.PLANE:
                return {
                    "type": "plane",
                    "size": self._estimate_size_from_shape(shape, default=2.0)
                }
            else:
                # Default to cube for unknown shapes
                return {
                    "type": "cube",
                    "size": 2.0
                }

        except Exception as e:
            logger.warning(f"Failed to create geometry from shape: {e}")
            return None

    def _create_transform_from_shape(self, shape: DetectedShape) -> Optional[Dict[str, Any]]:
        """Create transform specification from detected shape."""
        try:
            # Calculate position from bounding box center
            bbox = shape.bounding_box
            if bbox:
                # Convert image coordinates to 3D world coordinates
                # Assuming image center is (0.5, 0.5) maps to world origin (0, 0, 0)
                x = (bbox.x + bbox.width / 2 - 0.5) * 10  # Scale to reasonable world units
                y = 0.0  # Default Y position
                z = -(bbox.y + bbox.height / 2 - 0.5) * 10  # Flip Y to Z, invert for correct orientation

                return {
                    "position": {"x": x, "y": y, "z": z},
                    "rotation": {"x": 0.0, "y": 0.0, "z": 0.0},
                    "scale": {"x": 1.0, "y": 1.0, "z": 1.0}
                }

            return None

        except Exception as e:
            logger.warning(f"Failed to create transform from shape: {e}")
            return None

    def _create_material_from_shape(self, shape: DetectedShape) -> Optional[Dict[str, Any]]:
        """Create material specification from detected shape."""
        try:
            color_info = shape.color_info
            if color_info:
                return {
                    "type": self.config.default_material_type,
                    "name": f"{color_info.dominant_color_name}_material",
                    "color": {
                        "r": color_info.r,
                        "g": color_info.g,
                        "b": color_info.b,
                        "a": 1.0
                    },
                    "metallic": 0.0,
                    "roughness": 0.5
                }

            return None

        except Exception as e:
            logger.warning(f"Failed to create material from shape: {e}")
            return None

    def _estimate_size_from_shape(self, shape: DetectedShape, default: float = 1.0) -> float:
        """Estimate size from shape properties."""
        try:
            if shape.size_estimate and 'approximate_scale' in shape.size_estimate:
                return max(0.1, shape.size_estimate['approximate_scale'] * default)
            elif shape.bounding_box:
                # Use bounding box dimensions as size estimate
                bbox = shape.bounding_box
                avg_dimension = (bbox.width + bbox.height) / 2
                return max(0.1, avg_dimension * 10)  # Scale to reasonable world units
            else:
                return default

        except Exception as e:
            logger.warning(f"Failed to estimate size from shape: {e}")
            return default

    def _generate_description(self, image_analysis_result: ImageAnalysisResult) -> str:
        """Generate a description for the model."""
        try:
            shapes = image_analysis_result.detected_shapes
            if not shapes:
                return "3D model generated from image analysis"

            shape_counts = {}
            for shape in shapes:
                shape_type = shape.shape_type.value
                shape_counts[shape_type] = shape_counts.get(shape_type, 0) + 1

            # Build description
            parts = []
            for shape_type, count in shape_counts.items():
                if count == 1:
                    parts.append(f"a {shape_type}")
                else:
                    parts.append(f"{count} {shape_type}s")

            if len(parts) == 1:
                description = f"3D model containing {parts[0]}"
            elif len(parts) == 2:
                description = f"3D model containing {parts[0]} and {parts[1]}"
            else:
                description = f"3D model containing {', '.join(parts[:-1])}, and {parts[-1]}"

            # Add scene description if available
            if image_analysis_result.scene_description:
                description += f". {image_analysis_result.scene_description}"

            return description

        except Exception as e:
            logger.warning(f"Failed to generate description: {e}")
            return "3D model generated from image analysis"

    def _generate_tags(self, image_analysis_result: ImageAnalysisResult) -> List[str]:
        """Generate tags for the model."""
        try:
            tags = ["generated", "3d-model"]

            # Add shape-based tags
            for shape in image_analysis_result.detected_shapes:
                shape_tag = shape.shape_type.value
                if shape_tag not in tags:
                    tags.append(shape_tag)

            # Add color-based tags
            colors = set()
            for shape in image_analysis_result.detected_shapes:
                if shape.color_info and shape.color_info.dominant_color_name:
                    colors.add(shape.color_info.dominant_color_name)

            for color in colors:
                if len(tags) < 10:  # Respect max tags limit
                    tags.append(color)

            return tags[:10]  # Ensure we don't exceed max tags

        except Exception as e:
            logger.warning(f"Failed to generate tags: {e}")
            return ["generated", "3d-model"]

    def _create_default_object(self) -> Dict[str, Any]:
        """Create a default object when no shapes are detected."""
        return {
            "id": "default_cube",
            "name": "Default Cube",
            "geometry": {
                "type": "cube",
                "size": 2.0
            },
            "transform": {
                "position": {"x": 0.0, "y": 0.0, "z": 0.0},
                "rotation": {"x": 0.0, "y": 0.0, "z": 0.0},
                "scale": {"x": 1.0, "y": 1.0, "z": 1.0}
            },
            "material": {
                "type": "basic",
                "name": "default_material",
                "color": {"r": 0.8, "g": 0.8, "b": 0.8, "a": 1.0}
            },
            "visible": True
        }

    def _validate_specification(self, spec_dict: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate the generated specification."""
        errors = []

        try:
            # Level 1: Basic structure validation
            if self.config.validation_level in [ValidationLevel.BASIC, ValidationLevel.FULL]:
                basic_errors = self._validate_basic_structure(spec_dict)
                errors.extend(basic_errors)

            # Level 2: JSON Schema validation
            if self.config.validation_level in [ValidationLevel.SCHEMA, ValidationLevel.FULL]:
                if self.json_schema:
                    schema_errors = self._validate_json_schema(spec_dict)
                    errors.extend(schema_errors)

            # Level 3: Pydantic model validation
            if self.config.validation_level in [ValidationLevel.PYDANTIC, ValidationLevel.FULL]:
                pydantic_errors = self._validate_pydantic_model(spec_dict)
                errors.extend(pydantic_errors)

            return len(errors) == 0, errors

        except Exception as e:
            logger.error(f"Validation failed with exception: {e}")
            return False, [f"Validation exception: {str(e)}"]

    def _validate_basic_structure(self, spec_dict: Dict[str, Any]) -> List[str]:
        """Perform basic structure validation."""
        errors = []

        # Check required top-level fields
        required_fields = ["schema_version", "model_info", "objects"]
        for field in required_fields:
            if field not in spec_dict:
                errors.append(f"Missing required field: {field}")

        # Check objects array
        if "objects" in spec_dict:
            if not isinstance(spec_dict["objects"], list):
                errors.append("Objects field must be an array")
            elif len(spec_dict["objects"]) == 0:
                errors.append("Objects array cannot be empty")
            elif len(spec_dict["objects"]) > self.config.max_objects_per_spec:
                errors.append(f"Too many objects: {len(spec_dict['objects'])} > {self.config.max_objects_per_spec}")

        return errors

    def _validate_json_schema(self, spec_dict: Dict[str, Any]) -> List[str]:
        """Validate against JSON schema."""
        errors = []

        try:
            jsonschema.validate(spec_dict, self.json_schema)
        except jsonschema.ValidationError as e:
            # More detailed error reporting
            error_path = " -> ".join(str(p) for p in e.absolute_path)
            error_msg = f"JSON Schema validation error at {error_path}: {e.message}"
            if e.validator_value:
                error_msg += f" (expected: {e.validator_value}, got: {e.instance})"
            errors.append(error_msg)
        except jsonschema.SchemaError as e:
            errors.append(f"JSON Schema error: {e.message}")
        except Exception as e:
            errors.append(f"JSON Schema validation exception: {str(e)}")

        return errors

    def _validate_pydantic_model(self, spec_dict: Dict[str, Any]) -> List[str]:
        """Validate using Pydantic model."""
        errors = []

        try:
            # Choose the correct Pydantic model based on schema version
            schema_version = spec_dict.get("schema_version", "v1.0.0")

            if schema_version.startswith("v2."):
                # Import v2 model
                from models.specs.v2.models import ModelSpecificationV2
                ModelSpecificationV2(**spec_dict)
            else:
                # Use v1 model
                ModelSpecification(**spec_dict)

        except ValidationError as e:
            for error in e.errors():
                field_path = " -> ".join(str(loc) for loc in error['loc'])
                errors.append(f"Pydantic validation error in {field_path}: {error['msg']}")
        except Exception as e:
            errors.append(f"Pydantic validation exception: {str(e)}")

        return errors

    def _calculate_confidence_score(self,
                                  image_analysis_result: ImageAnalysisResult,
                                  spec_dict: Dict[str, Any],
                                  validation_passed: bool) -> float:
        """Calculate confidence score for the generated specification."""
        try:
            # Base confidence from image analysis
            base_confidence = image_analysis_result.overall_confidence

            # Validation bonus/penalty
            validation_factor = 1.0 if validation_passed else 0.5

            # Shape detection quality factor
            shape_factor = 1.0
            if image_analysis_result.detected_shapes:
                avg_shape_confidence = sum(
                    shape.confidence for shape in image_analysis_result.detected_shapes
                ) / len(image_analysis_result.detected_shapes)
                shape_factor = avg_shape_confidence

            # Object count factor (prefer reasonable number of objects)
            object_count = len(spec_dict.get("objects", []))
            if object_count == 0:
                object_factor = 0.1
            elif 1 <= object_count <= 5:
                object_factor = 1.0
            else:
                object_factor = max(0.5, 1.0 - (object_count - 5) * 0.1)

            # Combine factors
            final_confidence = base_confidence * validation_factor * shape_factor * object_factor

            return max(0.0, min(1.0, final_confidence))

        except Exception as e:
            logger.warning(f"Failed to calculate confidence score: {e}")
            return 0.5  # Default moderate confidence

    def _prepare_llm_context(self,
                           image_analysis_result: ImageAnalysisResult,
                           knowledge_context: List[RetrievalResult],
                           user_preferences: Optional[Dict[str, Any]]) -> str:
        """Prepare context text for LLM."""
        context_parts = []

        # Add image analysis results
        context_parts.append("IMAGE ANALYSIS RESULTS:")
        context_parts.append(f"Overall confidence: {image_analysis_result.overall_confidence}")
        context_parts.append(f"Scene description: {image_analysis_result.scene_description or 'None'}")
        context_parts.append("Detected shapes:")

        for i, shape in enumerate(image_analysis_result.detected_shapes):
            context_parts.append(f"  {i+1}. {shape.shape_type.value} (confidence: {shape.confidence})")
            if shape.color_info:
                context_parts.append(f"     Color: {shape.color_info.dominant_color_name}")
            if shape.bounding_box:
                bbox = shape.bounding_box
                context_parts.append(f"     Position: ({bbox.x:.2f}, {bbox.y:.2f})")

        # Add knowledge context
        if knowledge_context:
            context_parts.append("\nRELEVANT KNOWLEDGE:")
            for i, result in enumerate(knowledge_context[:3]):  # Limit to top 3
                context_parts.append(f"  {i+1}. {result.chunk.content[:200]}...")

        # Add user preferences
        if user_preferences:
            context_parts.append(f"\nUSER PREFERENCES: {json.dumps(user_preferences, indent=2)}")

        return "\n".join(context_parts)

    def _create_specification_prompt(self, context_text: str, model_name: Optional[str]) -> str:
        """Create prompt for LLM specification generation."""
        model_name_part = f"Model name: {model_name}\n" if model_name else ""

        return f"""You are a 3D modeling expert. Generate a JSON specification for a 3D model based on the provided image analysis results and context.

{context_text}

{model_name_part}
REQUIREMENTS:
1. Generate a valid JSON specification that conforms to the base_model_spec.json schema
2. Use schema version "v1.0.0"
3. Include all required fields: schema_version, model_info, objects
4. Each object must have: id, name, geometry
5. Supported geometry types: cube, sphere, cylinder, plane, cone
6. Use appropriate sizes, positions, and colors based on the image analysis
7. Ensure all IDs are unique and follow the pattern: alphanumeric, underscore, hyphen only

EXAMPLE OUTPUT FORMAT:
{{
  "schema_version": "v1.0.0",
  "model_info": {{
    "name": "Example Model",
    "description": "A 3D model containing basic shapes",
    "created_at": "2025-07-17T10:00:00Z",
    "tags": ["generated", "basic-shapes"]
  }},
  "scene_settings": {{
    "units": "meters"
  }},
  "objects": [
    {{
      "id": "cube_1",
      "name": "Red Cube",
      "geometry": {{
        "type": "cube",
        "size": 2.0
      }},
      "transform": {{
        "position": {{"x": 0.0, "y": 0.0, "z": 0.0}},
        "rotation": {{"x": 0.0, "y": 0.0, "z": 0.0}},
        "scale": {{"x": 1.0, "y": 1.0, "z": 1.0}}
      }},
      "material": {{
        "type": "basic",
        "name": "red_material",
        "color": {{"r": 1.0, "g": 0.0, "b": 0.0, "a": 1.0}}
      }},
      "visible": true
    }}
  ]
}}

Generate the JSON specification now:"""

    def _call_llm_with_retry(self, prompt: str) -> str:
        """Call LLM with retry mechanism."""
        for attempt in range(self.config.max_retries):
            try:
                response = self.openai_client.chat.completions.create(
                    model=self.config.openai_model,
                    messages=[
                        {"role": "system", "content": "You are a 3D modeling expert that generates JSON specifications."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1,  # Low temperature for consistent output
                    max_tokens=2000
                )

                return response.choices[0].message.content.strip()

            except Exception as e:
                logger.warning(f"LLM call attempt {attempt + 1} failed: {e}")
                if attempt == self.config.max_retries - 1:
                    raise SpecGenerationError(f"LLM call failed after {self.config.max_retries} attempts: {e}")

    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response to extract JSON specification."""
        try:
            # Try to find JSON in the response
            response = response.strip()

            # Look for JSON block markers
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_text = response[start:end].strip()
                else:
                    json_text = response[start:].strip()
            elif response.startswith("{"):
                json_text = response
            else:
                # Try to find the first { and last }
                start = response.find("{")
                end = response.rfind("}")
                if start != -1 and end != -1 and end > start:
                    json_text = response[start:end+1]
                else:
                    raise SpecGenerationError("No valid JSON found in LLM response")

            # Parse JSON
            spec_dict = json.loads(json_text)

            # Basic validation
            if not isinstance(spec_dict, dict):
                raise SpecGenerationError("LLM response is not a JSON object")

            return spec_dict

        except json.JSONDecodeError as e:
            raise SpecGenerationError(f"Failed to parse JSON from LLM response: {e}")
        except Exception as e:
            raise SpecGenerationError(f"Failed to parse LLM response: {e}")

    def validate_specification_only(self, spec_dict: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate a specification without generating it.

        Args:
            spec_dict: Specification dictionary to validate

        Returns:
            Tuple of (validation_passed, validation_errors)
        """
        return self._validate_specification(spec_dict)

    def get_supported_shapes(self) -> List[str]:
        """Get list of supported shape types."""
        return [shape.value for shape in GeometryTypeEnum]

    def get_schema_version(self) -> str:
        """Get the current schema version."""
        return self.config.schema_version

    # V2 specific methods
    def _determine_complexity_level(self,
                                  image_analysis_result: ImageAnalysisResult,
                                  user_preferences: Optional[Dict[str, Any]]) -> str:
        """Determine the complexity level based on image analysis and preferences."""
        if user_preferences and "complexity_level" in user_preferences:
            return user_preferences["complexity_level"]

        # Analyze image complexity
        num_shapes = len(image_analysis_result.detected_shapes)
        has_complex_shapes = any(
            shape.shape_type.value in ["torus", "pyramid", "ellipsoid", "tetrahedron", "octahedron"]
            for shape in image_analysis_result.detected_shapes
        )

        if num_shapes > 5 or has_complex_shapes:
            return "advanced"
        elif num_shapes > 2:
            return "intermediate"
        else:
            return "basic"

    def _determine_model_type(self,
                            image_analysis_result: ImageAnalysisResult,
                            user_preferences: Optional[Dict[str, Any]]) -> str:
        """Determine the model type based on image analysis and preferences."""
        if user_preferences and "model_type" in user_preferences:
            return user_preferences["model_type"]

        # Check for molecular/protein indicators in tags or description
        tags = self._generate_tags(image_analysis_result)
        description = self._generate_description(image_analysis_result)

        molecular_keywords = ["atom", "molecule", "chemical", "bond", "molecular"]
        protein_keywords = ["protein", "amino", "peptide", "enzyme", "antibody"]

        text_to_check = " ".join(tags + [description]).lower()

        if any(keyword in text_to_check for keyword in protein_keywords):
            return "protein"
        elif any(keyword in text_to_check for keyword in molecular_keywords):
            return "molecular"
        elif len(image_analysis_result.detected_shapes) > 3:
            return "composite"
        else:
            return "standard"

    def _should_add_lighting(self, image_analysis_result: ImageAnalysisResult) -> bool:
        """Determine if lighting should be added to the scene."""
        # Add lighting for scenes with multiple objects or complex materials
        return len(image_analysis_result.detected_shapes) > 1

    def _generate_lighting_config(self, image_analysis_result: ImageAnalysisResult) -> Dict[str, Any]:
        """Generate lighting configuration for the scene."""
        lights = []

        # Add a main sun light
        lights.append({
            "id": "main_sun",
            "type": "sun",
            "name": "Main Sun Light",
            "energy": 5.0,
            "color": {"r": 1.0, "g": 0.95, "b": 0.8, "a": 1.0},
            "transform": {
                "rotation": {
                    "x": 0.785398,  # 45 degrees
                    "y": 0.0,
                    "z": 0.785398   # 45 degrees
                }
            }
        })

        # Add fill light for complex scenes
        if len(image_analysis_result.detected_shapes) > 2:
            lights.append({
                "id": "fill_light",
                "type": "area",
                "name": "Fill Light",
                "energy": 2.0,
                "size": 2.0,
                "color": {"r": 0.8, "g": 0.9, "b": 1.0, "a": 1.0},
                "transform": {
                    "position": {"x": -3.0, "y": 3.0, "z": 2.0},
                    "rotation": {"x": 0.523599, "y": -0.785398, "z": 0.0}
                }
            })

        return {
            "lights": lights,
            "global_illumination": {
                "enabled": True,
                "strength": 1.0
            }
        }

    def _generate_mcp_structures(self,
                               image_analysis_result: ImageAnalysisResult,
                               model_type: str) -> List[Dict[str, Any]]:
        """Generate MCP structures based on model type."""
        structures = []

        if model_type == "molecular":
            # Create a simple molecular structure
            structures.append({
                "id": "molecule_1",
                "name": "Generated Molecule",
                "structure_type": "molecular",
                "molecular": {
                    "atoms": [
                        {
                            "id": "atom_1",
                            "element": "C",
                            "position": {"x": 0.0, "y": 0.0, "z": 0.0}
                        },
                        {
                            "id": "atom_2",
                            "element": "O",
                            "position": {"x": 1.5, "y": 0.0, "z": 0.0}
                        }
                    ],
                    "bonds": [
                        {
                            "atom1_id": "atom_1",
                            "atom2_id": "atom_2",
                            "bond_type": "double"
                        }
                    ],
                    "representation": "ball_and_stick"
                }
            })
        elif model_type == "protein":
            # Create a protein structure placeholder
            structures.append({
                "id": "protein_1",
                "name": "Generated Protein",
                "structure_type": "protein",
                "protein": {
                    "representation": "cartoon",
                    "color_scheme": "chain",
                    "show_sidechains": False,
                    "show_backbone": True
                }
            })

        return structures

    def _should_create_groups(self, image_analysis_result: ImageAnalysisResult) -> bool:
        """Determine if object groups should be created."""
        # Create groups for scenes with many objects
        return len(image_analysis_result.detected_shapes) > 3

    def _generate_object_groups(self, objects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate object groups based on object properties."""
        groups = []

        # Group objects by geometry type
        geometry_groups = {}
        for obj in objects:
            geom_type = obj.get("geometry", {}).get("type", "unknown")
            if geom_type not in geometry_groups:
                geometry_groups[geom_type] = []
            geometry_groups[geom_type].append(obj["id"])

        # Create groups for geometry types with multiple objects
        for geom_type, obj_ids in geometry_groups.items():
            if len(obj_ids) > 1:
                groups.append({
                    "id": f"{geom_type}_group",
                    "name": f"{geom_type.title()} Group",
                    "object_ids": obj_ids,
                    "visible": True
                })

        return groups

    def _shape_to_object_v2(self,
                          shape: DetectedShape,
                          index: int,
                          complexity_level: str) -> Optional[Dict[str, Any]]:
        """Convert a detected shape to a v2 3D object specification with advanced features."""
        try:
            # Generate unique ID
            obj_id = f"object_{index + 1}"

            # Map shape type to geometry
            geometry = self._create_geometry_from_shape(shape)
            if not geometry:
                return None

            # Create transform from shape properties
            transform = self._create_transform_from_shape(shape)

            # Create advanced material based on complexity
            material = self._create_advanced_material_from_shape(shape, complexity_level)

            # Build object specification
            obj = {
                "id": obj_id,
                "name": f"{shape.shape_type.value.title()}_{index + 1}",
                "geometry": geometry,
                "visible": True
            }

            # Add optional components
            if transform:
                obj["transform"] = transform

            if material:
                obj["material"] = material

            # Add modifiers based on complexity and shape
            if self.config.enable_modifiers and complexity_level in ["intermediate", "advanced"]:
                modifiers = self._generate_modifiers_for_shape(shape, complexity_level)
                if modifiers:
                    obj["modifiers"] = modifiers

            # Add physics for dynamic scenes
            if self.config.enable_physics and complexity_level == "advanced":
                obj["physics"] = self._generate_physics_properties(shape)

            return obj

        except Exception as e:
            logger.warning(f"Failed to convert shape to v2 object: {e}")
            return None

    def _create_advanced_material_from_shape(self,
                                           shape: DetectedShape,
                                           complexity_level: str) -> Optional[Dict[str, Any]]:
        """Create advanced material specification from detected shape."""
        try:
            color_info = shape.color_info
            if not color_info:
                return None

            base_material = {
                "name": f"{color_info.dominant_color_name}_material",
                "color": {
                    "r": color_info.r,
                    "g": color_info.g,
                    "b": color_info.b,
                    "a": 1.0
                }
            }

            # Choose material type based on complexity and color
            if complexity_level == "basic":
                return {**base_material, "type": "basic"}

            # Advanced material selection based on color properties
            if color_info.dominant_color_name in ["white", "silver", "gray"]:
                # Metallic materials
                return {
                    **base_material,
                    "type": "pbr",
                    "metallic": 0.8,
                    "roughness": 0.2
                }
            elif color_info.dominant_color_name in ["red", "orange", "yellow"]:
                # Emission materials for warm colors
                return {
                    **base_material,
                    "type": "emission",
                    "emission": base_material["color"],
                    "emission_strength": 2.0
                }
            elif color_info.dominant_color_name in ["blue", "cyan"]:
                # Glass materials for cool colors
                return {
                    **base_material,
                    "type": "glass",
                    "transmission": 0.9,
                    "ior": 1.45,
                    "roughness": 0.0
                }
            else:
                # Default PBR material
                return {
                    **base_material,
                    "type": "pbr",
                    "metallic": 0.0,
                    "roughness": 0.5
                }

        except Exception as e:
            logger.warning(f"Failed to create advanced material from shape: {e}")
            return None

    def _generate_modifiers_for_shape(self,
                                    shape: DetectedShape,
                                    complexity_level: str) -> List[Dict[str, Any]]:
        """Generate appropriate modifiers for a shape based on its properties."""
        modifiers = []

        try:
            shape_type = shape.shape_type.value

            # Add subdivision surface for smooth objects
            if shape_type in ["sphere", "cylinder", "torus"] and complexity_level == "advanced":
                modifiers.append({
                    "type": "subdivision_surface",
                    "name": "Smooth Surface",
                    "levels": 2,
                    "render_levels": 3,
                    "enabled": True
                })

            # Add mirror modifier for symmetric objects
            if shape_type in ["cube", "sphere"] and complexity_level in ["intermediate", "advanced"]:
                modifiers.append({
                    "type": "mirror",
                    "name": "Mirror X",
                    "axis": ["x"],
                    "merge": True,
                    "enabled": True
                })

            # Add array modifier for repeated patterns
            if complexity_level == "advanced" and shape_type in ["cube", "cylinder"]:
                modifiers.append({
                    "type": "array",
                    "name": "Array Pattern",
                    "count": 3,
                    "offset": {"x": 2.5, "y": 0.0, "z": 0.0},
                    "enabled": True
                })

            # Add bevel for hard-edged objects
            if shape_type in ["cube", "cone"] and complexity_level in ["intermediate", "advanced"]:
                modifiers.append({
                    "type": "bevel",
                    "name": "Edge Bevel",
                    "width": 0.1,
                    "segments": 2,
                    "enabled": True
                })

        except Exception as e:
            logger.warning(f"Failed to generate modifiers for shape: {e}")

        return modifiers

    def _generate_physics_properties(self, shape: DetectedShape) -> Dict[str, Any]:
        """Generate physics properties for a shape."""
        try:
            shape_type = shape.shape_type.value

            # Different physics properties based on shape
            if shape_type == "sphere":
                return {
                    "type": "dynamic",
                    "mass": 1.0,
                    "friction": 0.3,
                    "restitution": 0.8  # Bouncy
                }
            elif shape_type in ["cube", "cylinder"]:
                return {
                    "type": "dynamic",
                    "mass": 2.0,
                    "friction": 0.7,
                    "restitution": 0.1  # Less bouncy
                }
            elif shape_type == "plane":
                return {
                    "type": "static",
                    "mass": 0.0,
                    "friction": 0.8,
                    "restitution": 0.0
                }
            else:
                return {
                    "type": "dynamic",
                    "mass": 1.5,
                    "friction": 0.5,
                    "restitution": 0.3
                }

        except Exception as e:
            logger.warning(f"Failed to generate physics properties: {e}")
            return {
                "type": "none",
                "mass": 1.0,
                "friction": 0.5,
                "restitution": 0.0
            }

    def _create_default_object_v2(self) -> Dict[str, Any]:
        """Create a default v2 object when no shapes are detected."""
        return {
            "id": "default_cube",
            "name": "Default Cube",
            "geometry": {
                "type": "cube",
                "size": 2.0
            },
            "transform": {
                "position": {"x": 0.0, "y": 0.0, "z": 0.0},
                "rotation": {"x": 0.0, "y": 0.0, "z": 0.0},
                "scale": {"x": 1.0, "y": 1.0, "z": 1.0}
            },
            "material": {
                "type": "pbr",
                "name": "Default Material",
                "color": {"r": 0.8, "g": 0.8, "b": 0.8, "a": 1.0},
                "metallic": 0.0,
                "roughness": 0.5
            },
            "visible": True
        }

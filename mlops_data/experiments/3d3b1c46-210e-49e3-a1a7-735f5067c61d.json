{"experiment_id": "3d3b1c46-210e-49e3-a1a7-735f5067c61d", "name": "cv_model_training_v1", "description": "Training ResNet50 for shape detection", "created_at": "2025-07-19T12:39:05.656843", "updated_at": "2025-07-19T12:39:05.658566", "status": "completed", "parameters": {"batch_size": 32, "learning_rate": 0.001, "epochs": 50, "optimizer": "adam"}, "tags": [], "metrics_history": [{"step": 1, "timestamp": "2025-07-19T12:39:05.657027", "metrics": {"epoch": 1, "train_loss": 0.42, "val_loss": 0.53, "train_accuracy": 0.74, "val_accuracy": 0.6900000000000001}}, {"step": 2, "timestamp": "2025-07-19T12:39:05.657147", "metrics": {"epoch": 2, "train_loss": 0.33999999999999997, "val_loss": 0.45999999999999996, "train_accuracy": 0.7799999999999999, "val_accuracy": 0.73}}, {"step": 3, "timestamp": "2025-07-19T12:39:05.657456", "metrics": {"epoch": 3, "train_loss": 0.26, "val_loss": 0.38999999999999996, "train_accuracy": 0.82, "val_accuracy": 0.77}}, {"step": 4, "timestamp": "2025-07-19T12:39:05.657654", "metrics": {"epoch": 4, "train_loss": 0.18, "val_loss": 0.31999999999999995, "train_accuracy": 0.86, "val_accuracy": 0.81}}, {"step": 5, "timestamp": "2025-07-19T12:39:05.657883", "metrics": {"epoch": 5, "train_loss": 0.09999999999999998, "val_loss": 0.24999999999999994, "train_accuracy": 0.8999999999999999, "val_accuracy": 0.8500000000000001}}], "final_metrics": {"epoch": 5, "train_loss": 0.09999999999999998, "val_loss": 0.24999999999999994, "train_accuracy": 0.8999999999999999, "val_accuracy": 0.8500000000000001}, "artifacts": {"model_checkpoint": "/tmp/model_epoch_5.pth", "training_plot": "/tmp/training_curves.png"}, "model_type": "computer_vision", "framework": "pytorch", "dataset": "shapes_dataset_v1", "environment": {}, "git_commit": null}
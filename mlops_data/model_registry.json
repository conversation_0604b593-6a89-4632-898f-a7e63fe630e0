{"044f97a4-ec78-4bd0-8656-03ca46c98694": {"model_id": "044f97a4-ec78-4bd0-8656-03ca46c98694", "name": "image_analysis_cv_model", "model_type": "cv", "version": "1.0.0", "description": "ResNet50-based image analysis model for shape detection", "created_at": "2025-07-19T12:39:05.655415", "updated_at": "2025-07-19T12:39:05.656514", "status": "production", "tags": [], "framework": "pytorch", "architecture": "resnet50", "input_shape": null, "output_shape": null, "training_dataset": "custom_shapes_dataset_v1", "training_parameters": null, "performance_metrics": {"accuracy": 0.92, "f1_score": 0.89}, "model_path": null, "config_path": null, "weights_path": null, "checksum": null, "parent_model_id": null, "experiment_id": null}, "bdd0014f-398b-41be-975a-ea02e58c2fa5": {"model_id": "bdd0014f-398b-41be-975a-ea02e58c2fa5", "name": "code_generation_llm", "model_type": "llm", "version": "1.0.0", "description": "GPT-4 configuration for Blender Python code generation", "created_at": "2025-07-19T12:39:05.655589", "updated_at": "2025-07-19T12:39:05.655589", "status": "development", "tags": [], "framework": "openai_api", "architecture": "gpt-4", "input_shape": null, "output_shape": null, "training_dataset": null, "training_parameters": {"temperature": 0.1, "max_tokens": 2048, "top_p": 0.95}, "performance_metrics": null, "model_path": null, "config_path": null, "weights_path": null, "checksum": null, "parent_model_id": null, "experiment_id": null}, "ba892e5d-b19a-4f2c-a883-1ef5bfd37a8f": {"model_id": "ba892e5d-b19a-4f2c-a883-1ef5bfd37a8f", "name": "knowledge_agent_rl_policy", "model_type": "rl", "version": "1.0.0", "description": "PPO policy for knowledge agent tool selection", "created_at": "2025-07-19T12:39:05.655773", "updated_at": "2025-07-19T12:39:05.655773", "status": "development", "tags": [], "framework": "ray_rllib", "architecture": "ppo", "input_shape": null, "output_shape": null, "training_dataset": null, "training_parameters": {"learning_rate": 0.0003, "gamma": 0.99, "lambda": 0.95}, "performance_metrics": {"avg_reward": 0.75, "success_rate": 0.82}, "model_path": null, "config_path": null, "weights_path": null, "checksum": null, "parent_model_id": null, "experiment_id": null}}
"""
Comprehensive unit tests for ImageAnalysisAgent

Tests cover:
- Basic shape recognition functionality
- Multi-modal LLM integration (mocked)
- Confidence scoring and validation
- Error handling and edge cases
- Output format validation
- Performance requirements
"""

import unittest
import tempfile
import shutil
import json
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
from PIL import Image
import numpy as np

from agents.image_analysis_agent import (
    ImageAnalysisAgent,
    ShapeType,
    AnalysisGranularity,
    DetectedShape,
    BoundingBox,
    ColorInfo,
    ImageAnalysisResult,
    ImageAnalysisError
)


class TestImageAnalysisAgent(unittest.TestCase):
    """Test cases for ImageAnalysisAgent."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = tempfile.mkdtemp()
        self.agent = ImageAnalysisAgent()
        
        # Create test images
        self._create_test_images()
    
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.test_dir)
    
    def _create_test_images(self):
        """Create test images for different shapes."""
        # Create simple colored squares to simulate different shapes
        self.test_images = {}
        
        # Red square (cube)
        red_img = Image.new('RGB', (256, 256), color='red')
        self.test_images['cube'] = os.path.join(self.test_dir, 'red_cube.png')
        red_img.save(self.test_images['cube'])
        
        # Blue circle (sphere) - create a simple circle
        blue_img = Image.new('RGB', (256, 256), color='white')
        # Draw a blue circle (simplified)
        import numpy as np
        arr = np.array(blue_img)
        center = (128, 128)
        radius = 80
        y, x = np.ogrid[:256, :256]
        mask = (x - center[0])**2 + (y - center[1])**2 <= radius**2
        arr[mask] = [0, 0, 255]  # Blue
        blue_img = Image.fromarray(arr)
        self.test_images['sphere'] = os.path.join(self.test_dir, 'blue_sphere.png')
        blue_img.save(self.test_images['sphere'])
        
        # Green rectangle (cylinder)
        green_img = Image.new('RGB', (256, 256), color='white')
        arr = np.array(green_img)
        arr[64:192, 96:160] = [0, 255, 0]  # Green rectangle
        green_img = Image.fromarray(arr)
        self.test_images['cylinder'] = os.path.join(self.test_dir, 'green_cylinder.png')
        green_img.save(self.test_images['cylinder'])
        
        # Yellow triangle (cone)
        yellow_img = Image.new('RGB', (256, 256), color='white')
        arr = np.array(yellow_img)
        # Simple triangle
        for i in range(64, 192):
            width = (i - 64) // 2
            start = 128 - width
            end = 128 + width
            arr[i, start:end] = [255, 255, 0]  # Yellow
        yellow_img = Image.fromarray(arr)
        self.test_images['cone'] = os.path.join(self.test_dir, 'yellow_cone.png')
        yellow_img.save(self.test_images['cone'])
        
        # Empty image (no shapes)
        empty_img = Image.new('RGB', (256, 256), color='white')
        self.test_images['empty'] = os.path.join(self.test_dir, 'empty.png')
        empty_img.save(self.test_images['empty'])
    
    def _create_mock_vision_response(self, shape_type: str, confidence: float = 0.95) -> str:
        """Create mock vision model response."""
        response_data = {
            "detected_shapes": [
                {
                    "shape_type": shape_type,
                    "confidence": confidence,
                    "bounding_box": {"x": 0.2, "y": 0.2, "width": 0.6, "height": 0.6},
                    "color_info": {
                        "r": 1.0 if shape_type == "cube" else 0.0,
                        "g": 1.0 if shape_type == "cylinder" else 0.0,
                        "b": 1.0 if shape_type == "sphere" else 0.0,
                        "dominant_color_name": "red" if shape_type == "cube" else "blue" if shape_type == "sphere" else "green"
                    },
                    "size_estimate": {"relative_size": "medium", "approximate_scale": 1.0}
                }
            ],
            "scene_description": f"A {shape_type} positioned in the center of the image",
            "overall_confidence": confidence
        }
        return json.dumps(response_data)
    
    def test_initialization_without_api_key(self):
        """Test agent initialization without API key."""
        agent = ImageAnalysisAgent()
        self.assertIsNone(agent.openai_client)
        self.assertIsNotNone(agent.supported_shapes)
    
    def test_initialization_with_api_key(self):
        """Test agent initialization with API key."""
        with patch('agents.image_analysis_agent.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_openai.return_value = mock_client
            
            agent = ImageAnalysisAgent(openai_api_key="test_key")
            self.assertEqual(agent.openai_client, mock_client)
    
    def test_get_supported_shapes(self):
        """Test getting supported shape types."""
        shapes = self.agent.get_supported_shapes()
        # Basic shapes should still be supported
        basic_shapes = {'cube', 'sphere', 'cylinder', 'cone', 'plane'}
        self.assertTrue(basic_shapes.issubset(set(shapes)))
        # Should also include complex shapes now
        self.assertGreater(len(shapes), 5)
    
    def test_analyze_image_file_not_found(self):
        """Test analysis with non-existent file."""
        with self.assertRaises(ImageAnalysisError):
            self.agent.analyze_image("non_existent.png")
    
    def test_analyze_image_without_api_key(self):
        """Test analysis without OpenAI API key."""
        with self.assertRaises(ImageAnalysisError) as context:
            self.agent.analyze_image(self.test_images['cube'])
        
        self.assertIn("OpenAI client not initialized", str(context.exception))
    
    @patch('agents.image_analysis_agent.OpenAI')
    def test_analyze_cube_success(self, mock_openai_class):
        """Test successful cube analysis."""
        # Setup mock
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = self._create_mock_vision_response("cube", 0.95)
        mock_client.chat.completions.create.return_value = mock_response
        
        # Create agent with mocked client
        agent = ImageAnalysisAgent(openai_api_key="test_key")
        agent.openai_client = mock_client
        
        # Analyze image
        result = agent.analyze_image(self.test_images['cube'])
        
        # Validate result
        self.assertIsInstance(result, ImageAnalysisResult)
        self.assertEqual(len(result.detected_shapes), 1)
        self.assertEqual(result.detected_shapes[0].shape_type, ShapeType.CUBE)
        self.assertEqual(result.detected_shapes[0].confidence, 0.95)
        self.assertGreaterEqual(result.overall_confidence, 0.85)  # Task requirement
    
    @patch('agents.image_analysis_agent.OpenAI')
    def test_analyze_sphere_success(self, mock_openai_class):
        """Test successful sphere analysis."""
        # Setup mock
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = self._create_mock_vision_response("sphere", 0.92)
        mock_client.chat.completions.create.return_value = mock_response
        
        # Create agent with mocked client
        agent = ImageAnalysisAgent(openai_api_key="test_key")
        agent.openai_client = mock_client
        
        # Analyze image
        result = agent.analyze_image(self.test_images['sphere'])
        
        # Validate result
        self.assertIsInstance(result, ImageAnalysisResult)
        self.assertEqual(len(result.detected_shapes), 1)
        self.assertEqual(result.detected_shapes[0].shape_type, ShapeType.SPHERE)
        self.assertEqual(result.detected_shapes[0].confidence, 0.92)
        self.assertGreaterEqual(result.overall_confidence, 0.85)
    
    @patch('agents.image_analysis_agent.OpenAI')
    def test_analyze_cylinder_success(self, mock_openai_class):
        """Test successful cylinder analysis."""
        # Setup mock
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = self._create_mock_vision_response("cylinder", 0.88)
        mock_client.chat.completions.create.return_value = mock_response
        
        # Create agent with mocked client
        agent = ImageAnalysisAgent(openai_api_key="test_key")
        agent.openai_client = mock_client
        
        # Analyze image
        result = agent.analyze_image(self.test_images['cylinder'])
        
        # Validate result
        self.assertIsInstance(result, ImageAnalysisResult)
        self.assertEqual(len(result.detected_shapes), 1)
        self.assertEqual(result.detected_shapes[0].shape_type, ShapeType.CYLINDER)
        self.assertEqual(result.detected_shapes[0].confidence, 0.88)
        self.assertGreaterEqual(result.overall_confidence, 0.85)
    
    @patch('agents.image_analysis_agent.OpenAI')
    def test_analyze_empty_image(self, mock_openai_class):
        """Test analysis of empty image."""
        # Setup mock for empty response
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        empty_response = {
            "detected_shapes": [],
            "scene_description": "Empty white background with no recognizable shapes",
            "overall_confidence": 0.1
        }
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps(empty_response)
        mock_client.chat.completions.create.return_value = mock_response
        
        # Create agent with mocked client
        agent = ImageAnalysisAgent(openai_api_key="test_key")
        agent.openai_client = mock_client
        
        # Analyze image
        result = agent.analyze_image(self.test_images['empty'])
        
        # Validate result
        self.assertIsInstance(result, ImageAnalysisResult)
        self.assertEqual(len(result.detected_shapes), 0)
        self.assertLessEqual(result.overall_confidence, 0.5)
    
    @patch('agents.image_analysis_agent.OpenAI')
    def test_vision_model_retry_mechanism(self, mock_openai_class):
        """Test retry mechanism for vision model failures."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # First two calls fail, third succeeds
        mock_client.chat.completions.create.side_effect = [
            Exception("API Error 1"),
            Exception("API Error 2"),
            Mock(choices=[Mock(message=Mock(content=self._create_mock_vision_response("cube", 0.9)))])
        ]
        
        agent = ImageAnalysisAgent(openai_api_key="test_key")
        agent.openai_client = mock_client
        
        with patch('time.sleep'):  # Speed up test
            result = agent.analyze_image(self.test_images['cube'])
        
        # Should succeed after retries
        self.assertIsInstance(result, ImageAnalysisResult)
        self.assertEqual(mock_client.chat.completions.create.call_count, 3)
    
    @patch('agents.image_analysis_agent.OpenAI')
    def test_vision_model_max_retries_exceeded(self, mock_openai_class):
        """Test behavior when max retries are exceeded."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # All calls fail
        mock_client.chat.completions.create.side_effect = Exception("Persistent API Error")
        
        agent = ImageAnalysisAgent(openai_api_key="test_key")
        agent.openai_client = mock_client
        
        with patch('time.sleep'):  # Speed up test
            with self.assertRaises(ImageAnalysisError):
                agent.analyze_image(self.test_images['cube'])
        
        self.assertEqual(mock_client.chat.completions.create.call_count, 3)  # MAX_RETRIES
    
    def test_detected_shape_to_dict(self):
        """Test DetectedShape serialization."""
        bbox = BoundingBox(x=0.1, y=0.2, width=0.3, height=0.4)
        color = ColorInfo(r=1.0, g=0.0, b=0.0, dominant_color_name="red")
        
        shape = DetectedShape(
            shape_type=ShapeType.CUBE,
            confidence=0.95,
            bounding_box=bbox,
            color_info=color,
            size_estimate={"relative_size": "large"}
        )
        
        result_dict = shape.to_dict()
        
        self.assertEqual(result_dict['shape_type'], 'cube')
        self.assertEqual(result_dict['confidence'], 0.95)
        self.assertIn('bounding_box', result_dict)
        self.assertIn('color_info', result_dict)
        self.assertIn('size_estimate', result_dict)
    
    def test_image_analysis_result_to_dict(self):
        """Test ImageAnalysisResult serialization."""
        shape = DetectedShape(
            shape_type=ShapeType.SPHERE,
            confidence=0.9
        )
        
        result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[shape],
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC,
            scene_description="Test scene",
            processing_time=1.5
        )
        
        result_dict = result.to_dict()
        
        self.assertEqual(result_dict['image_path'], "test.png")
        self.assertEqual(len(result_dict['detected_shapes']), 1)
        self.assertEqual(result_dict['overall_confidence'], 0.9)
        self.assertEqual(result_dict['analysis_granularity'], 'basic')
        self.assertEqual(result_dict['processing_time'], 1.5)
    
    def test_validate_analysis_result_valid(self):
        """Test validation of valid analysis result."""
        shape = DetectedShape(
            shape_type=ShapeType.CUBE,
            confidence=0.95
        )
        
        result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[shape],
            overall_confidence=0.95,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        self.assertTrue(self.agent.validate_analysis_result(result))
    
    def test_validate_analysis_result_invalid_confidence(self):
        """Test validation with invalid confidence values."""
        shape = DetectedShape(
            shape_type=ShapeType.CUBE,
            confidence=1.5  # Invalid: > 1.0
        )
        
        result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[shape],
            overall_confidence=0.95,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        self.assertFalse(self.agent.validate_analysis_result(result))
    
    def test_parse_malformed_json_response(self):
        """Test handling of malformed JSON responses."""
        with patch('agents.image_analysis_agent.OpenAI') as mock_openai_class:
            mock_client = Mock()
            mock_openai_class.return_value = mock_client
            
            # Return malformed JSON
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "This is not valid JSON"
            mock_client.chat.completions.create.return_value = mock_response
            
            agent = ImageAnalysisAgent(openai_api_key="test_key")
            agent.openai_client = mock_client
            
            result = agent.analyze_image(self.test_images['cube'])
            
            # Should return empty result with low confidence
            self.assertEqual(len(result.detected_shapes), 0)
            self.assertEqual(result.overall_confidence, 0.0)
            self.assertIn("Failed to parse", result.scene_description)
    
    @patch('agents.image_analysis_agent.OpenAI')
    def test_confidence_score_requirements(self, mock_openai_class):
        """Test that confidence scores meet task requirements (>85% accuracy)."""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # Test multiple shapes with high confidence
        test_cases = [
            ("cube", 0.95),
            ("sphere", 0.92),
            ("cylinder", 0.88),
            ("cone", 0.90),
        ]
        
        agent = ImageAnalysisAgent(openai_api_key="test_key")
        agent.openai_client = mock_client
        
        for shape_type, expected_confidence in test_cases:
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = self._create_mock_vision_response(shape_type, expected_confidence)
            mock_client.chat.completions.create.return_value = mock_response
            
            result = agent.analyze_image(self.test_images.get(shape_type, self.test_images['cube']))
            
            # Verify confidence meets requirements
            self.assertGreaterEqual(result.overall_confidence, 0.85, 
                                  f"Confidence for {shape_type} should be >= 85%")
            self.assertEqual(result.detected_shapes[0].confidence, expected_confidence)


if __name__ == '__main__':
    unittest.main()

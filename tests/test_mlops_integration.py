"""
Test suite for MLOps integration and functionality.

This module tests the complete MLOps infrastructure including model registry,
experiment tracking, version management, and storage backends.
"""

import json
import pytest
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
from unittest.mock import patch, MagicMock

from mlops import (
    ModelRegistry, ModelType, ModelStatus,
    ExperimentTracker, Experiment, ExperimentMetrics,
    ModelVersionManager, ModelVersion,
    LocalStorageBackend, ModelArtifactManager
)


class TestModelRegistry:
    """Test cases for ModelRegistry functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.registry_path = Path(self.temp_dir) / "test_registry.json"
        self.registry = ModelRegistry(self.registry_path)
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_register_model(self):
        """Test model registration."""
        model_id = self.registry.register_model(
            name="test_cv_model",
            model_type=ModelType.COMPUTER_VISION,
            version="1.0.0",
            description="Test CV model",
            framework="pytorch",
            performance_metrics={"accuracy": 0.95}
        )
        
        assert model_id is not None
        assert len(model_id) == 36  # UUID length
        
        # Verify model was registered
        model = self.registry.get_model(model_id)
        assert model is not None
        assert model.name == "test_cv_model"
        assert model.model_type == ModelType.COMPUTER_VISION
        assert model.version == "1.0.0"
        assert model.performance_metrics["accuracy"] == 0.95
    
    def test_list_models_by_type(self):
        """Test listing models by type."""
        # Register different types of models
        cv_id = self.registry.register_model(
            name="cv_model", model_type=ModelType.COMPUTER_VISION,
            version="1.0.0", description="CV model"
        )
        llm_id = self.registry.register_model(
            name="llm_model", model_type=ModelType.LANGUAGE_MODEL,
            version="1.0.0", description="LLM model"
        )
        
        # Test filtering by type
        cv_models = self.registry.list_models(model_type=ModelType.COMPUTER_VISION)
        llm_models = self.registry.list_models(model_type=ModelType.LANGUAGE_MODEL)
        
        assert len(cv_models) == 1
        assert len(llm_models) == 1
        assert cv_models[0].model_id == cv_id
        assert llm_models[0].model_id == llm_id
    
    def test_update_model_status(self):
        """Test updating model status."""
        model_id = self.registry.register_model(
            name="test_model", model_type=ModelType.CUSTOM,
            version="1.0.0", description="Test model"
        )
        
        # Update status
        success = self.registry.update_model(model_id, status=ModelStatus.PRODUCTION)
        assert success
        
        # Verify update
        model = self.registry.get_model(model_id)
        assert model.status == ModelStatus.PRODUCTION
    
    def test_get_model_by_name_version(self):
        """Test getting model by name and version."""
        self.registry.register_model(
            name="test_model", model_type=ModelType.CUSTOM,
            version="1.0.0", description="Test model v1"
        )
        self.registry.register_model(
            name="test_model", model_type=ModelType.CUSTOM,
            version="2.0.0", description="Test model v2"
        )
        
        model_v1 = self.registry.get_model_by_name_version("test_model", "1.0.0")
        model_v2 = self.registry.get_model_by_name_version("test_model", "2.0.0")
        
        assert model_v1 is not None
        assert model_v2 is not None
        assert model_v1.version == "1.0.0"
        assert model_v2.version == "2.0.0"
    
    def test_persistence(self):
        """Test registry persistence."""
        # Register a model
        model_id = self.registry.register_model(
            name="persistent_model", model_type=ModelType.EMBEDDING,
            version="1.0.0", description="Test persistence"
        )
        
        # Create new registry instance (simulating restart)
        new_registry = ModelRegistry(self.registry_path)
        
        # Verify model is still there
        model = new_registry.get_model(model_id)
        assert model is not None
        assert model.name == "persistent_model"


class TestExperimentTracker:
    """Test cases for ExperimentTracker functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.experiments_dir = Path(self.temp_dir) / "experiments"
        self.tracker = ExperimentTracker(self.experiments_dir)
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_create_experiment(self):
        """Test experiment creation."""
        exp_id = self.tracker.create_experiment(
            name="test_experiment",
            description="Test experiment",
            parameters={"lr": 0.001, "batch_size": 32},
            tags=["test", "cv"]
        )
        
        assert exp_id is not None
        
        # Verify experiment was created
        experiment = self.tracker.get_experiment(exp_id)
        assert experiment is not None
        assert experiment.name == "test_experiment"
        assert experiment.parameters["lr"] == 0.001
        assert "test" in experiment.tags
    
    def test_log_metrics(self):
        """Test logging metrics."""
        exp_id = self.tracker.create_experiment(
            name="metrics_test", description="Test metrics logging"
        )
        
        # Log metrics for multiple steps
        for step in range(3):
            metrics = {"loss": 1.0 - step * 0.1, "accuracy": 0.5 + step * 0.1}
            success = self.tracker.log_metrics(exp_id, metrics, step=step)
            assert success
        
        # Verify metrics were logged
        experiment = self.tracker.get_experiment(exp_id)
        assert len(experiment.metrics_history) == 3
        assert experiment.final_metrics["accuracy"] == 0.7  # Last logged value
    
    def test_log_artifacts(self):
        """Test logging artifacts."""
        exp_id = self.tracker.create_experiment(
            name="artifacts_test", description="Test artifact logging"
        )
        
        # Log artifacts
        success1 = self.tracker.log_artifact(exp_id, "model", "/path/to/model.pth")
        success2 = self.tracker.log_artifact(exp_id, "plot", "/path/to/plot.png")
        
        assert success1 and success2
        
        # Verify artifacts were logged
        experiment = self.tracker.get_experiment(exp_id)
        assert "model" in experiment.artifacts
        assert "plot" in experiment.artifacts
        assert experiment.artifacts["model"] == "/path/to/model.pth"
    
    def test_finish_experiment(self):
        """Test finishing experiments."""
        exp_id = self.tracker.create_experiment(
            name="finish_test", description="Test finishing"
        )
        
        # Finish experiment
        success = self.tracker.finish_experiment(exp_id, status="completed")
        assert success
        
        # Verify status
        experiment = self.tracker.get_experiment(exp_id)
        assert experiment.status == "completed"
    
    def test_get_best_experiment(self):
        """Test finding best experiment."""
        # Create multiple experiments with different metrics
        exp_ids = []
        for i in range(3):
            exp_id = self.tracker.create_experiment(
                name=f"exp_{i}", description=f"Experiment {i}"
            )
            self.tracker.log_metrics(exp_id, {"accuracy": 0.8 + i * 0.05})
            self.tracker.finish_experiment(exp_id, "completed")
            exp_ids.append(exp_id)
        
        # Find best experiment
        best_exp = self.tracker.get_best_experiment("accuracy", maximize=True)
        assert best_exp is not None
        assert best_exp.final_metrics["accuracy"] == 0.9  # Highest value
    
    def test_compare_experiments(self):
        """Test experiment comparison."""
        # Create two experiments
        exp1_id = self.tracker.create_experiment("exp1", "First experiment")
        exp2_id = self.tracker.create_experiment("exp2", "Second experiment")
        
        self.tracker.log_metrics(exp1_id, {"accuracy": 0.85, "f1": 0.82})
        self.tracker.log_metrics(exp2_id, {"accuracy": 0.90, "f1": 0.88})
        
        # Compare experiments
        comparison = self.tracker.compare_experiments(
            [exp1_id, exp2_id], metrics=["accuracy", "f1"]
        )
        
        assert exp1_id in comparison["experiments"]
        assert exp2_id in comparison["experiments"]
        assert "accuracy" in comparison["metrics_comparison"]
        assert comparison["metrics_comparison"]["accuracy"][exp2_id] == 0.90


class TestModelVersionManager:
    """Test cases for ModelVersionManager functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.models_dir = Path(self.temp_dir) / "models"
        self.vm = ModelVersionManager(self.models_dir)
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_create_version(self):
        """Test version creation."""
        version = self.vm.create_version(
            model_name="test_model",
            version="1.0.0",
            description="Initial version",
            performance_metrics={"accuracy": 0.85}
        )
        
        assert version is not None
        assert version.model_name == "test_model"
        assert version.version == "1.0.0"
        assert version.major == 1
        assert version.minor == 0
        assert version.patch == 0
    
    def test_get_latest_version(self):
        """Test getting latest version."""
        # Create multiple versions
        self.vm.create_version("test_model", "1.0.0", "v1.0.0")
        self.vm.create_version("test_model", "1.1.0", "v1.1.0")
        self.vm.create_version("test_model", "1.0.1", "v1.0.1")
        
        latest = self.vm.get_latest_version("test_model")
        assert latest is not None
        assert latest.version == "1.1.0"  # Highest version
    
    def test_promote_version(self):
        """Test version promotion."""
        self.vm.create_version("test_model", "1.0.0", "Initial version")
        
        # Promote to stable
        success = self.vm.promote_version("test_model", "1.0.0", stable=True)
        assert success
        
        # Verify promotion
        version = self.vm.get_version("test_model", "1.0.0")
        assert version.is_stable
    
    def test_version_comparison(self):
        """Test version comparison."""
        self.vm.create_version("test_model", "1.0.0", "v1", performance_metrics={"acc": 0.8})
        self.vm.create_version("test_model", "1.1.0", "v2", performance_metrics={"acc": 0.9})
        
        comparison = self.vm.compare_versions("test_model", "1.0.0", "1.1.0")
        
        assert comparison["model_name"] == "test_model"
        assert comparison["version1"]["version"] == "1.0.0"
        assert comparison["version2"]["version"] == "1.1.0"
        assert comparison["compatibility"]  # Same major version
        assert comparison["newer_version"] == "1.1.0"
    
    def test_suggest_next_version(self):
        """Test version suggestion."""
        self.vm.create_version("test_model", "1.2.3", "Current version")
        
        next_patch = self.vm.suggest_next_version("test_model", "patch")
        next_minor = self.vm.suggest_next_version("test_model", "minor")
        next_major = self.vm.suggest_next_version("test_model", "major")
        
        assert next_patch == "1.2.4"
        assert next_minor == "1.3.0"
        assert next_major == "2.0.0"


class TestStorageBackend:
    """Test cases for storage backend functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.storage_dir = Path(self.temp_dir) / "storage"
        self.storage = LocalStorageBackend(self.storage_dir)
        self.artifact_manager = ModelArtifactManager(self.storage)
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_save_and_load_file(self):
        """Test file save and load operations."""
        # Create test file
        test_file = Path(self.temp_dir) / "test.txt"
        test_file.write_text("test content")
        
        # Save file
        success = self.storage.save_file(test_file, "models/test.txt")
        assert success
        
        # Load file
        loaded_file = Path(self.temp_dir) / "loaded.txt"
        success = self.storage.load_file("models/test.txt", loaded_file)
        assert success
        assert loaded_file.read_text() == "test content"
    
    def test_file_operations(self):
        """Test various file operations."""
        # Create and save test file
        test_file = Path(self.temp_dir) / "test.txt"
        test_file.write_text("test")
        self.storage.save_file(test_file, "test.txt")
        
        # Test file exists
        assert self.storage.file_exists("test.txt")
        assert not self.storage.file_exists("nonexistent.txt")
        
        # Test list files
        files = self.storage.list_files()
        assert "test.txt" in files
        
        # Test get file info
        info = self.storage.get_file_info("test.txt")
        assert info is not None
        assert info["size"] == 4  # "test" is 4 bytes
        
        # Test delete file
        success = self.storage.delete_file("test.txt")
        assert success
        assert not self.storage.file_exists("test.txt")
    
    def test_model_artifact_management(self):
        """Test model artifact management."""
        # Create test model file
        model_file = Path(self.temp_dir) / "model.pth"
        model_file.write_text("fake model data")
        
        # Save model artifact
        remote_path = self.artifact_manager.save_model_artifact(
            "test_model", "1.0.0", "model.pth", model_file
        )
        assert remote_path == "models/test_model/1.0.0/model.pth"
        
        # List artifacts
        artifacts = self.artifact_manager.list_model_artifacts("test_model", "1.0.0")
        assert "model.pth" in artifacts
        
        # Load artifact
        loaded_file = Path(self.temp_dir) / "loaded_model.pth"
        success = self.artifact_manager.load_model_artifact(
            "test_model", "1.0.0", "model.pth", loaded_file
        )
        assert success
        assert loaded_file.read_text() == "fake model data"


class TestMLOpsIntegration:
    """Test cases for complete MLOps integration."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        
        # Initialize all components
        self.registry = ModelRegistry(Path(self.temp_dir) / "registry.json")
        self.tracker = ExperimentTracker(Path(self.temp_dir) / "experiments")
        self.vm = ModelVersionManager(Path(self.temp_dir) / "models")
        self.storage = LocalStorageBackend(Path(self.temp_dir) / "storage")
    
    def teardown_method(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir)
    
    def test_end_to_end_workflow(self):
        """Test complete end-to-end MLOps workflow."""
        # 1. Create experiment
        exp_id = self.tracker.create_experiment(
            name="e2e_test",
            description="End-to-end test",
            parameters={"lr": 0.001}
        )
        
        # 2. Log training metrics
        self.tracker.log_metrics(exp_id, {"accuracy": 0.95, "loss": 0.05})
        
        # 3. Create model file
        model_file = Path(self.temp_dir) / "model.pth"
        model_file.write_text("trained model")
        
        # 4. Log artifact
        self.tracker.log_artifact(exp_id, "model", str(model_file))
        
        # 5. Finish experiment
        self.tracker.finish_experiment(exp_id, "completed")
        
        # 6. Register model
        model_id = self.registry.register_model(
            name="e2e_model",
            model_type=ModelType.COMPUTER_VISION,
            version="1.0.0",
            description="End-to-end test model",
            experiment_id=exp_id,
            performance_metrics={"accuracy": 0.95}
        )
        
        # 7. Create version
        version = self.vm.create_version(
            model_name="e2e_model",
            version="1.0.0",
            description="Initial version",
            model_path=str(model_file),
            performance_metrics={"accuracy": 0.95}
        )
        
        # 8. Verify everything is connected
        model = self.registry.get_model(model_id)
        experiment = self.tracker.get_experiment(exp_id)
        model_version = self.vm.get_version("e2e_model", "1.0.0")
        
        assert model.experiment_id == exp_id
        assert experiment.status == "completed"
        assert model_version.model_name == "e2e_model"
        assert model.performance_metrics["accuracy"] == 0.95
    
    def test_model_lifecycle_management(self):
        """Test complete model lifecycle."""
        # Development phase
        model_id = self.registry.register_model(
            name="lifecycle_model",
            model_type=ModelType.LANGUAGE_MODEL,
            version="1.0.0",
            description="Lifecycle test model"
        )
        
        self.vm.create_version(
            model_name="lifecycle_model",
            version="1.0.0",
            description="Development version"
        )
        
        # Testing phase
        self.registry.update_model(model_id, status=ModelStatus.TESTING)
        
        # Production promotion
        self.registry.update_model(model_id, status=ModelStatus.PRODUCTION)
        self.vm.promote_version("lifecycle_model", "1.0.0", stable=True)
        
        # Create new version
        self.vm.create_version(
            model_name="lifecycle_model",
            version="1.1.0",
            description="Improved version",
            parent_version="1.0.0"
        )
        
        # Deprecate old version
        self.vm.deprecate_version("lifecycle_model", "1.0.0")
        
        # Verify lifecycle states
        model = self.registry.get_model(model_id)
        v1 = self.vm.get_version("lifecycle_model", "1.0.0")
        v11 = self.vm.get_version("lifecycle_model", "1.1.0")
        
        assert model.status == ModelStatus.PRODUCTION
        assert v1.is_stable and v1.is_deprecated
        assert not v11.is_deprecated
        assert v11.parent_version == "1.0.0"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

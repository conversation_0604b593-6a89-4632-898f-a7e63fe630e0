"""
Basic integration test for AutoGen + RL framework (Task 1.2)

This test verifies the core functionality without requiring complex dependencies.
"""

import unittest
import sys
import os
import json

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from agents.autogen_om_poc import (
    AgentCommunicationProtocol, 
    SimpleToolSelectionEnv, 
    RLEnhancedAgent
)

class TestBasicIntegration(unittest.TestCase):
    """Test basic integration functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.protocol = AgentCommunicationProtocol()
    
    def test_communication_protocol(self):
        """Test agent communication protocol."""
        print("\n--- Testing Communication Protocol ---")
        
        # Create a message
        message = self.protocol.create_message(
            sender_id="test_agent",
            receiver_id="target_agent",
            message_type="test_message",
            payload={"data": "test_payload"},
            metadata={"confidence": 0.95}
        )
        
        # Validate message structure
        self.assertIn("message_id", message)
        self.assertIn("sender_id", message)
        self.assertIn("receiver_id", message)
        self.assertIn("message_type", message)
        self.assertIn("timestamp", message)
        self.assertIn("payload", message)
        self.assertIn("metadata", message)
        
        # Validate message content
        self.assertEqual(message["sender_id"], "test_agent")
        self.assertEqual(message["receiver_id"], "target_agent")
        self.assertEqual(message["message_type"], "test_message")
        self.assertEqual(message["payload"]["data"], "test_payload")
        self.assertEqual(message["metadata"]["confidence"], 0.95)
        
        # Test message validation
        self.assertTrue(self.protocol.validate_message(message))
        
        print(f"✅ Message created and validated successfully")
        print(f"   Message ID: {message['message_id']}")
        print(f"   Timestamp: {message['timestamp']}")
    
    def test_simple_tool_selection_env(self):
        """Test the simple tool selection environment."""
        print("\n--- Testing Tool Selection Environment ---")
        
        env = SimpleToolSelectionEnv()
        
        # Test environment initialization
        self.assertEqual(len(env.available_tools), 4)
        self.assertIn("print_hello_world", env.available_tools)
        self.assertIn("analyze_image", env.available_tools)
        self.assertIn("generate_spec", env.available_tools)
        self.assertIn("generate_code", env.available_tools)
        
        print(f"✅ Environment initialized with {len(env.available_tools)} tools")
        
        # Test reset
        state, info = env.reset()
        self.assertEqual(len(state), 4)
        self.assertEqual(env.step_count, 0)
        
        print(f"✅ Environment reset successfully")
        print(f"   Initial state: {state}")
        
        # Test step execution
        action = 1  # analyze_image
        new_state, reward, done, truncated, step_info = env.step(action)
        
        self.assertEqual(len(new_state), 4)
        self.assertIsInstance(reward, (int, float))
        self.assertIsInstance(done, bool)
        self.assertIn("selected_tool", step_info)
        self.assertEqual(step_info["selected_tool"], "analyze_image")
        
        print(f"✅ Step executed successfully")
        print(f"   Selected tool: {step_info['selected_tool']}")
        print(f"   Reward: {reward}")
        print(f"   New state: {new_state}")
    
    def test_rl_enhanced_agent(self):
        """Test the RL-enhanced agent."""
        print("\n--- Testing RL Enhanced Agent ---")
        
        agent = RLEnhancedAgent("test_rl_agent")
        
        # Test agent initialization
        self.assertEqual(agent.agent_id, "test_rl_agent")
        self.assertIsNotNone(agent.communication)
        self.assertIsNotNone(agent.env)
        self.assertIsNotNone(agent.algorithm)
        
        print(f"✅ RL Agent initialized successfully")
        print(f"   Agent ID: {agent.agent_id}")
        
        # Test tool selection
        context = {"message_content": "analyze this image"}
        selected_tool = agent.select_tool_with_rl(context)
        
        self.assertIn(selected_tool, agent.env.available_tools)
        
        print(f"✅ Tool selection working")
        print(f"   Selected tool: {selected_tool}")
        
        # Test message sending
        message = agent.send_message(
            receiver_id="target_agent",
            message_type="tool_selection",
            payload={"selected_tool": selected_tool}
        )
        
        self.assertTrue(agent.communication.validate_message(message))
        self.assertEqual(message["sender_id"], "test_rl_agent")
        
        print(f"✅ Message sending working")
        print(f"   Message type: {message['message_type']}")
    
    def test_agent_conversation_flow(self):
        """Test a complete agent conversation flow."""
        print("\n--- Testing Agent Conversation Flow ---")
        
        # Create two agents
        agent1 = RLEnhancedAgent("ImageAnalysisAgent")
        agent2 = RLEnhancedAgent("SpecGenerationAgent")
        
        # Agent 1 sends image analysis result
        analysis_message = agent1.send_message(
            receiver_id="SpecGenerationAgent",
            message_type="image_analysis_result",
            payload={
                "source_image_path": "/test/image.png",
                "detected_objects": [
                    {"label": "cube", "bbox": [10, 10, 50, 50]},
                    {"label": "sphere", "bbox": [60, 60, 100, 100]}
                ],
                "confidence_score": 0.95  # Move metadata into payload
            }
        )
        
        # Agent 2 responds with spec generation
        spec_message = agent2.send_message(
            receiver_id="ImageAnalysisAgent",
            message_type="spec_generation_result",
            payload={
                "model_spec": {
                    "objects": [
                        {"type": "cube", "size": 2.0, "position": [0, 0, 0]},
                        {"type": "sphere", "radius": 1.0, "position": [3, 0, 0]}
                    ]
                }
            }
        )
        
        # Validate conversation
        self.assertTrue(agent1.communication.validate_message(analysis_message))
        self.assertTrue(agent2.communication.validate_message(spec_message))
        
        self.assertEqual(analysis_message["message_type"], "image_analysis_result")
        self.assertEqual(spec_message["message_type"], "spec_generation_result")
        
        print(f"✅ Agent conversation flow completed")
        print(f"   Messages exchanged: 2")
        print(f"   All messages valid: True")
    
    def test_multiple_tool_selections(self):
        """Test multiple tool selections with different contexts."""
        print("\n--- Testing Multiple Tool Selections ---")
        
        agent = RLEnhancedAgent("multi_tool_agent")
        
        test_contexts = [
            {"message_content": "analyze this image"},
            {"message_content": "generate a specification"},
            {"message_content": "create blender code"},
            {"message_content": "print hello world"}
        ]
        
        selected_tools = []
        for i, context in enumerate(test_contexts):
            tool = agent.select_tool_with_rl(context)
            selected_tools.append(tool)
            print(f"   Context {i+1}: '{context['message_content']}' -> {tool}")
        
        # Verify all selections are valid tools
        for tool in selected_tools:
            self.assertIn(tool, agent.env.available_tools)
        
        print(f"✅ Multiple tool selections completed")
        print(f"   Total selections: {len(selected_tools)}")
        print(f"   All selections valid: True")

def run_basic_tests():
    """Run basic integration tests."""
    print("=== Running Basic AutoGen + RL Integration Tests ===")
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add tests
    suite.addTest(TestBasicIntegration('test_communication_protocol'))
    suite.addTest(TestBasicIntegration('test_simple_tool_selection_env'))
    suite.addTest(TestBasicIntegration('test_rl_enhanced_agent'))
    suite.addTest(TestBasicIntegration('test_agent_conversation_flow'))
    suite.addTest(TestBasicIntegration('test_multiple_tool_selections'))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=0)
    result = runner.run(suite)
    
    return result

if __name__ == "__main__":
    result = run_basic_tests()
    
    print(f"\n=== Test Summary ===")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}")
            print(f"  {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}")
            print(f"  {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\nSuccess rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("✅ Basic integration tests PASSED!")
        print("\n🎉 Task 1.2 implementation is working correctly!")
    else:
        print("❌ Some tests failed. Please review the implementation.")

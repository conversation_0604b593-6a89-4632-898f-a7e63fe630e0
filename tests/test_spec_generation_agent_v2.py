"""
Tests for SpecGenerationAgent v2.0.0 functionality.

This module tests the enhanced features of the specification generation agent,
including complex materials, modifiers, lighting, and MCP structures.
"""

import unittest
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from agents.spec_generation_agent import SpecGeneration<PERSON>gent, SpecGenerationConfig
from agents.image_analysis_agent import (
    ImageAnalysisResult, DetectedShape, ShapeType, ColorInfo, BoundingBox, AnalysisGranularity
)
from models.specs.v2.models import ModelSpecificationV2
from models.specs.v2.migration import migrate_v1_to_v2, migrate_v2_to_v1, validate_migration


class TestSpecGenerationAgentV2(unittest.TestCase):
    """Test cases for v2.0.0 specification generation."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create v2 configuration
        self.config_v2 = SpecGenerationConfig(
            schema_version="v2.0.0",
            enable_complex_materials=True,
            enable_modifiers=True,
            enable_lighting=True,
            enable_mcp_structures=True,
            default_complexity_level="intermediate"
        )
        
        # Create agent with v2 config
        self.agent = SpecGenerationAgent(config=self.config_v2)
        
        # Sample test data
        self.sample_bbox = BoundingBox(x=10, y=10, width=40, height=40)
        self.sample_color = ColorInfo(
            r=0.2, g=0.4, b=0.8,
            dominant_color_name="blue"
        )
        
    def test_v2_schema_loading(self):
        """Test that v2 schema is loaded correctly."""
        self.assertEqual(self.agent.get_schema_version(), "v2.0.0")
        self.assertIsNotNone(self.agent.json_schema)
        self.assertEqual(self.agent.json_schema.get("version"), "2.0.0")
    
    def test_v2_basic_specification_generation(self):
        """Test basic v2 specification generation."""
        # Create test shape
        test_shape = DetectedShape(
            shape_type=ShapeType.SPHERE,
            confidence=0.9,
            bounding_box=self.sample_bbox,
            color_info=self.sample_color
        )
        
        test_result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[test_shape],
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        # Generate specification
        result = self.agent.generate_specification(test_result)
        
        # Validate v2 structure
        if not result.validation_passed:
            print(f"Validation errors: {result.validation_errors}")
            print(f"Generated spec: {result.specification}")
        self.assertTrue(result.validation_passed)
        self.assertTrue(result.specification["schema_version"].startswith("v2."))
        
        # Check v2-specific fields
        model_info = result.specification["model_info"]
        self.assertIn("complexity_level", model_info)
        self.assertIn("model_type", model_info)
        
        # Validate with Pydantic model
        spec_model = ModelSpecificationV2(**result.specification)
        self.assertIsInstance(spec_model, ModelSpecificationV2)
    
    def test_complex_materials_generation(self):
        """Test generation of complex materials."""
        # Test different color scenarios for different material types
        test_cases = [
            (ColorInfo(r=0.8, g=0.8, b=0.8, dominant_color_name="silver"), "pbr"),  # Metallic
            (ColorInfo(r=0.8, g=0.2, b=0.2, dominant_color_name="red"), "emission"),  # Emission
            (ColorInfo(r=0.2, g=0.4, b=0.8, dominant_color_name="blue"), "glass"),  # Glass
        ]
        
        for color_info, expected_type in test_cases:
            with self.subTest(color=color_info.dominant_color_name):
                test_shape = DetectedShape(
                    shape_type=ShapeType.SPHERE,
                    confidence=0.9,
                    bounding_box=self.sample_bbox,
                    color_info=color_info
                )
                
                test_result = ImageAnalysisResult(
                    image_path="test.png",
                    detected_shapes=[test_shape],
                    overall_confidence=0.9,
                    analysis_granularity=AnalysisGranularity.BASIC
                )
                
                result = self.agent.generate_specification(test_result)
                
                self.assertTrue(result.validation_passed)
                material = result.specification["objects"][0]["material"]
                self.assertEqual(material["type"], expected_type)
    
    def test_modifiers_generation(self):
        """Test generation of modifiers for objects."""
        # Test with advanced complexity to trigger modifiers
        test_shape = DetectedShape(
            shape_type=ShapeType.CUBE,
            confidence=0.9,
            bounding_box=self.sample_bbox,
            color_info=self.sample_color
        )
        
        test_result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[test_shape],
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        # Generate with advanced complexity
        user_prefs = {"complexity_level": "advanced"}
        result = self.agent.generate_specification(test_result, user_prefs)
        
        self.assertTrue(result.validation_passed)
        obj = result.specification["objects"][0]
        
        # Check for modifiers
        if "modifiers" in obj:
            self.assertIsInstance(obj["modifiers"], list)
            if obj["modifiers"]:
                modifier = obj["modifiers"][0]
                self.assertIn("type", modifier)
                self.assertIn("name", modifier)
                self.assertIn("enabled", modifier)
    
    def test_lighting_generation(self):
        """Test automatic lighting generation."""
        # Create multiple shapes to trigger lighting
        shapes = [
            DetectedShape(ShapeType.CUBE, 0.9, self.sample_bbox, self.sample_color),
            DetectedShape(ShapeType.SPHERE, 0.8, self.sample_bbox, self.sample_color)
        ]
        
        test_result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=shapes,
            overall_confidence=0.85,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        result = self.agent.generate_specification(test_result)
        
        self.assertTrue(result.validation_passed)
        
        # Check for lighting configuration
        if "lighting" in result.specification:
            lighting = result.specification["lighting"]
            self.assertIn("lights", lighting)
            self.assertIsInstance(lighting["lights"], list)
            self.assertGreater(len(lighting["lights"]), 0)
            
            # Validate light structure
            light = lighting["lights"][0]
            self.assertIn("id", light)
            self.assertIn("type", light)
            self.assertIn("name", light)
            self.assertIn("energy", light)
    
    def test_mcp_structures_generation(self):
        """Test MCP structures generation."""
        # Test molecular type
        test_result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[DetectedShape(ShapeType.SPHERE, 0.9, self.sample_bbox, self.sample_color)],
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        user_prefs = {"model_type": "molecular"}
        result = self.agent.generate_specification(test_result, user_prefs)
        
        self.assertTrue(result.validation_passed)
        
        # Check for MCP structures
        if "mcp_structures" in result.specification:
            mcp_structures = result.specification["mcp_structures"]
            self.assertIsInstance(mcp_structures, list)
            
            if mcp_structures:
                structure = mcp_structures[0]
                self.assertIn("id", structure)
                self.assertIn("name", structure)
                self.assertIn("structure_type", structure)
                self.assertEqual(structure["structure_type"], "molecular")
    
    def test_object_groups_generation(self):
        """Test automatic object groups generation."""
        # Create many shapes to trigger grouping
        shapes = [
            DetectedShape(ShapeType.CUBE, 0.9, self.sample_bbox, self.sample_color),
            DetectedShape(ShapeType.CUBE, 0.8, self.sample_bbox, self.sample_color),
            DetectedShape(ShapeType.SPHERE, 0.9, self.sample_bbox, self.sample_color),
            DetectedShape(ShapeType.SPHERE, 0.8, self.sample_bbox, self.sample_color),
        ]
        
        test_result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=shapes,
            overall_confidence=0.85,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        result = self.agent.generate_specification(test_result)
        
        self.assertTrue(result.validation_passed)
        
        # Check for groups
        if "groups" in result.specification:
            groups = result.specification["groups"]
            self.assertIsInstance(groups, list)
            
            if groups:
                group = groups[0]
                self.assertIn("id", group)
                self.assertIn("name", group)
                self.assertIn("object_ids", group)
                self.assertIsInstance(group["object_ids"], list)
    
    def test_physics_properties_generation(self):
        """Test physics properties generation."""
        test_shape = DetectedShape(
            shape_type=ShapeType.SPHERE,
            confidence=0.9,
            bounding_box=self.sample_bbox,
            color_info=self.sample_color
        )
        
        test_result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[test_shape],
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        # Enable physics and set advanced complexity
        config = SpecGenerationConfig(
            schema_version="v2.0.0",
            enable_physics=True
        )
        agent = SpecGenerationAgent(config=config)
        
        user_prefs = {"complexity_level": "advanced"}
        result = agent.generate_specification(test_result, user_prefs)
        
        self.assertTrue(result.validation_passed)
        obj = result.specification["objects"][0]
        
        # Check for physics properties
        if "physics" in obj:
            physics = obj["physics"]
            self.assertIn("type", physics)
            self.assertIn("mass", physics)
            self.assertIn("friction", physics)
            self.assertIn("restitution", physics)
    
    def test_complexity_level_determination(self):
        """Test automatic complexity level determination."""
        # Test basic complexity (single simple shape)
        simple_result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=[DetectedShape(ShapeType.CUBE, 0.9, self.sample_bbox, self.sample_color)],
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        result = self.agent.generate_specification(simple_result)
        self.assertEqual(result.specification["model_info"]["complexity_level"], "basic")
        
        # Test advanced complexity (many shapes)
        complex_shapes = [
            DetectedShape(ShapeType.TORUS, 0.9, self.sample_bbox, self.sample_color)
            for _ in range(6)
        ]
        complex_result = ImageAnalysisResult(
            image_path="test.png",
            detected_shapes=complex_shapes,
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        result = self.agent.generate_specification(complex_result)
        self.assertEqual(result.specification["model_info"]["complexity_level"], "advanced")
    
    def test_model_type_determination(self):
        """Test automatic model type determination."""
        # Test with molecular keywords in description
        test_result = ImageAnalysisResult(
            image_path="molecule.png",
            detected_shapes=[DetectedShape(ShapeType.SPHERE, 0.9, self.sample_bbox, self.sample_color)],
            overall_confidence=0.9,
            analysis_granularity=AnalysisGranularity.BASIC
        )
        
        result = self.agent.generate_specification(test_result)
        # The model type determination is based on image path and tags
        # This test verifies the mechanism works
        self.assertIn(result.specification["model_info"]["model_type"], 
                     ["standard", "molecular", "protein", "composite"])


class TestSchemaMigration(unittest.TestCase):
    """Test cases for schema migration between v1 and v2."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_v1_spec = {
            "schema_version": "v1.0.0",
            "model_info": {
                "name": "Test Model",
                "description": "A test model",
                "created_at": "2025-01-19T10:00:00Z",
                "tags": ["test", "cube"]
            },
            "scene_settings": {
                "units": "meters"
            },
            "objects": [
                {
                    "id": "cube_1",
                    "name": "Test Cube",
                    "geometry": {
                        "type": "cube",
                        "size": 2.0
                    },
                    "material": {
                        "type": "basic",
                        "name": "Red Material",
                        "color": {"r": 0.8, "g": 0.2, "b": 0.2, "a": 1.0}
                    },
                    "visible": True
                }
            ]
        }
    
    def test_v1_to_v2_migration(self):
        """Test migration from v1 to v2."""
        v2_spec = migrate_v1_to_v2(self.sample_v1_spec)
        
        # Check v2 structure
        self.assertTrue(v2_spec["schema_version"].startswith("v2."))
        self.assertIn("complexity_level", v2_spec["model_info"])
        self.assertIn("model_type", v2_spec["model_info"])
        
        # Validate with Pydantic
        spec_model = ModelSpecificationV2(**v2_spec)
        self.assertIsInstance(spec_model, ModelSpecificationV2)
    
    def test_v2_to_v1_migration(self):
        """Test migration from v2 to v1."""
        # First migrate v1 to v2
        v2_spec = migrate_v1_to_v2(self.sample_v1_spec)
        
        # Then migrate back to v1
        v1_spec = migrate_v2_to_v1(v2_spec)
        
        # Check v1 structure
        self.assertTrue(v1_spec["schema_version"].startswith("v1."))
        self.assertNotIn("complexity_level", v1_spec["model_info"])
        self.assertNotIn("model_type", v1_spec["model_info"])
    
    def test_migration_validation(self):
        """Test migration validation."""
        v2_spec = migrate_v1_to_v2(self.sample_v1_spec)
        
        # Validate migration preserved essential data
        self.assertTrue(validate_migration(self.sample_v1_spec, v2_spec))
        
        # Check that object count is preserved
        self.assertEqual(
            len(self.sample_v1_spec["objects"]),
            len(v2_spec["objects"])
        )
        
        # Check that model name is preserved
        self.assertEqual(
            self.sample_v1_spec["model_info"]["name"],
            v2_spec["model_info"]["name"]
        )


if __name__ == '__main__':
    unittest.main()

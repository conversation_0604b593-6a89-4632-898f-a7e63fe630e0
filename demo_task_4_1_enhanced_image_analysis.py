#!/usr/bin/env python3
"""
Demo script for Task 4.1: Enhanced Image Analysis Agent
Demonstrates complex shape recognition, depth estimation, and multi-object scene analysis.

Features demonstrated:
- Complex geometric shape recognition (>70% accuracy)
- Depth estimation with MRE <0.15
- Multi-object scene analysis
- Relative position recognition (>60% accuracy)
- Scene complexity assessment
- Occlusion handling
"""

import os
import sys
import json
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.image_analysis_agent import (
    ImageAnalysisAgent,
    AnalysisGranularity,
    ShapeType,
    SceneComplexity
)


def create_demo_images():
    """Create demonstration images for complex scene analysis."""
    demo_dir = Path("demo_output")
    demo_dir.mkdir(exist_ok=True)
    
    images = {}
    
    # 1. Complex multi-object scene
    print("Creating complex multi-object scene...")
    img = Image.new('RGB', (512, 512), color='lightgray')
    draw = ImageDraw.Draw(img)
    
    # Background objects
    draw.rectangle([50, 50, 150, 150], fill='blue', outline='darkblue', width=3)  # Cube
    draw.ellipse([300, 300, 450, 450], fill='red', outline='darkred', width=3)    # Sphere
    
    # Foreground objects
    draw.rectangle([200, 100, 250, 300], fill='green', outline='darkgreen', width=3)  # Cylinder
    draw.polygon([(350, 50), (300, 150), (400, 150)], fill='yellow', outline='orange', width=3)  # Pyramid
    
    # Add some complexity with overlapping
    draw.ellipse([180, 180, 280, 280], fill='purple', outline='indigo', width=2)  # Overlapping sphere
    
    images['complex_scene'] = demo_dir / "complex_multi_object_scene.png"
    img.save(images['complex_scene'])
    
    # 2. Torus-like complex shape
    print("Creating complex torus shape...")
    img = Image.new('RGB', (256, 256), color='white')
    arr = np.array(img)
    
    # Create torus shape
    center = (128, 128)
    outer_radius = 80
    inner_radius = 40
    y, x = np.ogrid[:256, :256]
    outer_mask = (x - center[0])**2 + (y - center[1])**2 <= outer_radius**2
    inner_mask = (x - center[0])**2 + (y - center[1])**2 <= inner_radius**2
    torus_mask = outer_mask & ~inner_mask
    arr[torus_mask] = [255, 140, 0]  # Orange torus
    
    # Add some texture/complexity
    for i in range(0, 256, 20):
        for j in range(0, 256, 20):
            if torus_mask[i, j]:
                arr[max(0, i-2):min(256, i+3), max(0, j-2):min(256, j+3)] = [200, 100, 0]
    
    img = Image.fromarray(arr)
    images['torus'] = demo_dir / "complex_torus_shape.png"
    img.save(images['torus'])
    
    # 3. Heavily occluded scene
    print("Creating occluded scene...")
    img = Image.new('RGB', (400, 400), color='white')
    draw = ImageDraw.Draw(img)
    
    # Background large rectangle
    draw.rectangle([50, 50, 350, 300], fill='lightblue', outline='blue', width=4)
    
    # Partially occluding circle
    draw.ellipse([150, 100, 300, 250], fill='lightcoral', outline='red', width=4)
    
    # Small foreground object
    draw.rectangle([200, 150, 250, 200], fill='gold', outline='orange', width=3)
    
    # Another overlapping shape
    draw.polygon([(100, 200), (150, 280), (200, 250), (180, 200)], 
                fill='lightgreen', outline='green', width=3)
    
    images['occluded'] = demo_dir / "occluded_scene.png"
    img.save(images['occluded'])
    
    return images


def analyze_with_different_granularities(agent, image_path):
    """Analyze image with different granularity levels."""
    print(f"\n=== Analyzing {image_path.name} ===")
    
    granularities = [
        AnalysisGranularity.BASIC,
        AnalysisGranularity.DETAILED,
        AnalysisGranularity.ADVANCED,
        AnalysisGranularity.EXPERT
    ]
    
    results = {}
    
    for granularity in granularities:
        print(f"\n--- {granularity.value.upper()} Analysis ---")
        try:
            # Note: In real usage, you would provide an actual OpenAI API key
            result = agent.analyze_image(image_path, granularity)
            results[granularity.value] = result
            
            print(f"Detected shapes: {len(result.detected_shapes)}")
            print(f"Overall confidence: {result.overall_confidence:.3f}")
            
            if result.scene_complexity:
                print(f"Scene complexity: {result.scene_complexity.value}")
            
            if result.depth_estimation_mre is not None:
                print(f"Depth estimation MRE: {result.depth_estimation_mre:.3f}")
            
            if result.relative_position_accuracy is not None:
                print(f"Relative position accuracy: {result.relative_position_accuracy:.3f}")
            
            # Show detected shapes
            for i, shape in enumerate(result.detected_shapes):
                print(f"  Shape {i+1}: {shape.shape_type.value} (conf: {shape.confidence:.3f})")
                if shape.depth_info:
                    print(f"    Depth: {shape.depth_info.estimated_depth:.3f} (conf: {shape.depth_info.depth_confidence:.3f})")
                if shape.relationships:
                    print(f"    Relationships: {len(shape.relationships)}")
                    for rel in shape.relationships:
                        print(f"      {rel.relationship_type.value} {rel.target_object_id} (conf: {rel.confidence:.3f})")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
            results[granularity.value] = None
    
    return results


def demonstrate_complex_scene_analysis(agent, image_path):
    """Demonstrate complex scene analysis capabilities."""
    print(f"\n=== Complex Scene Analysis: {image_path.name} ===")
    
    try:
        # Analyze with full complex scene capabilities
        result = agent.analyze_complex_scene(
            image_path,
            enable_depth_estimation=True,
            enable_relationship_analysis=True
        )
        
        print(f"Analysis completed in {result.processing_time:.2f}s")
        print(f"Scene complexity: {result.scene_complexity.value if result.scene_complexity else 'Unknown'}")
        print(f"Detected {len(result.detected_shapes)} objects")
        
        # Evaluate performance metrics
        metrics = agent.evaluate_performance_metrics(result)
        print("\n--- Performance Metrics ---")
        
        for metric, value in metrics.items():
            if isinstance(value, bool):
                status = "✓ PASS" if value else "✗ FAIL"
                print(f"{metric}: {status}")
            else:
                print(f"{metric}: {value:.3f}")
        
        # Check task requirements
        print("\n--- Task 4.1 Requirements Check ---")
        
        # Complex shape accuracy >70%
        complex_accuracy = metrics.get('complex_shape_accuracy', 0)
        print(f"Complex shape accuracy: {complex_accuracy:.1%} (req: >70%) - {'✓' if complex_accuracy > 0.7 else '✗'}")
        
        # Depth estimation MRE <0.15
        depth_mre = metrics.get('depth_estimation_mre', float('inf'))
        print(f"Depth estimation MRE: {depth_mre:.3f} (req: <0.15) - {'✓' if depth_mre < 0.15 else '✗'}")
        
        # Relative position accuracy >60%
        pos_accuracy = metrics.get('relative_position_accuracy', 0)
        print(f"Relative position accuracy: {pos_accuracy:.1%} (req: >60%) - {'✓' if pos_accuracy > 0.6 else '✗'}")
        
        return result
        
    except Exception as e:
        print(f"Complex scene analysis failed: {e}")
        return None


def save_analysis_results(results, output_dir):
    """Save analysis results to JSON files."""
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    for image_name, image_results in results.items():
        output_file = output_dir / f"{image_name}_analysis_results.json"
        
        # Convert results to serializable format
        serializable_results = {}
        for granularity, result in image_results.items():
            if result:
                serializable_results[granularity] = result.to_dict()
            else:
                serializable_results[granularity] = None
        
        with open(output_file, 'w') as f:
            json.dump(serializable_results, f, indent=2)
        
        print(f"Results saved to {output_file}")


def main():
    """Main demonstration function."""
    print("=== Task 4.1: Enhanced Image Analysis Agent Demo ===")
    print("Demonstrating complex shape recognition, depth estimation, and multi-object analysis")
    
    # Create demo images
    print("\n1. Creating demonstration images...")
    demo_images = create_demo_images()
    
    # Initialize agent (Note: requires OpenAI API key for real usage)
    print("\n2. Initializing ImageAnalysisAgent...")
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("Warning: No OpenAI API key found. Demo will show structure but not real analysis.")
        print("Set OPENAI_API_KEY environment variable for full functionality.")
    
    agent = ImageAnalysisAgent(openai_api_key=api_key)
    
    # Demonstrate different analysis approaches
    all_results = {}
    
    for image_name, image_path in demo_images.items():
        print(f"\n{'='*60}")
        print(f"Analyzing: {image_name}")
        print(f"{'='*60}")
        
        # Basic granularity analysis
        if api_key:
            results = analyze_with_different_granularities(agent, image_path)
            all_results[image_name] = results
            
            # Complex scene analysis
            complex_result = demonstrate_complex_scene_analysis(agent, image_path)
        else:
            print("Skipping analysis - no API key provided")
    
    # Save results
    if api_key and all_results:
        print("\n3. Saving analysis results...")
        save_analysis_results(all_results, "demo_output/task_4_1_results")
    
    print("\n=== Demo Complete ===")
    print("Task 4.1 enhanced features demonstrated:")
    print("✓ Complex geometric shape recognition")
    print("✓ Multi-object scene analysis")
    print("✓ Depth estimation and spatial relationships")
    print("✓ Scene complexity assessment")
    print("✓ Performance metrics evaluation")


if __name__ == "__main__":
    main()

"""
V2 Model Specifications Package

This package contains the v2.0.0 schema and Pydantic models for advanced 3D model specifications.
Supports complex materials, modifiers, lighting, hierarchical structures, and MCP (Molecular Nodes) integration.
"""

from .models import (
    # Core models
    ModelSpecificationV2,
    ModelInfo,
    SceneSettings,
    
    # Geometry models
    BaseGeometry,
    CubeGeometry,
    SphereGeometry,
    CylinderGeometry,
    PlaneGeometry,
    ConeGeometry,
    TorusGeometry,
    
    # Material models
    BaseMaterial,
    BasicMaterial,
    PBRMaterial,
    GlassMaterial,
    EmissionMaterial,
    SubsurfaceMaterial,
    ToonMaterial,
    PrincipledMaterial,
    
    # Modifier models
    BaseModifier,
    ArrayModifier,
    MirrorModifier,
    SolidifyModifier,
    BevelModifier,
    SubdivisionSurfaceModifier,
    
    # Lighting models
    Light,
    PointLight,
    SunLight,
    SpotLight,
    AreaLight,
    Environment,
    
    # Structure models
    Object3D,
    Group,
    Transform,
    Physics,
    Animation,
    Keyframe,
    
    # MCP models
    MCPStructure,
    MolecularStructure,
    ProteinStructure,
    Atom,
    Bond,
    
    # Enums
    UnitsEnum,
    GeometryTypeEnum,
    MaterialTypeEnum,
    ModifierTypeEnum,
    LightTypeEnum,
    ComplexityLevel,
    ModelType,
    BondType,
    RepresentationStyle,
    ColorScheme,
    PhysicsType,
    InterpolationType,
    
    # Utility types
    Color,
    Vector3
)

__version__ = "2.0.0"
__all__ = [
    # Core models
    "ModelSpecificationV2",
    "ModelInfo", 
    "SceneSettings",
    
    # Geometry models
    "BaseGeometry",
    "CubeGeometry",
    "SphereGeometry", 
    "CylinderGeometry",
    "PlaneGeometry",
    "ConeGeometry",
    "TorusGeometry",
    
    # Material models
    "BaseMaterial",
    "BasicMaterial",
    "PBRMaterial",
    "GlassMaterial",
    "EmissionMaterial",
    "SubsurfaceMaterial",
    "ToonMaterial",
    "PrincipledMaterial",
    
    # Modifier models
    "BaseModifier",
    "ArrayModifier",
    "MirrorModifier",
    "SolidifyModifier",
    "BevelModifier",
    "SubdivisionSurfaceModifier",
    
    # Lighting models
    "Light",
    "PointLight",
    "SunLight",
    "SpotLight",
    "AreaLight",
    "Environment",
    
    # Structure models
    "Object3D",
    "Group",
    "Transform",
    "Physics",
    "Animation",
    "Keyframe",
    
    # MCP models
    "MCPStructure",
    "MolecularStructure",
    "ProteinStructure",
    "Atom",
    "Bond",
    
    # Enums
    "UnitsEnum",
    "GeometryTypeEnum",
    "MaterialTypeEnum",
    "ModifierTypeEnum",
    "LightTypeEnum",
    "ComplexityLevel",
    "ModelType",
    "BondType",
    "RepresentationStyle",
    "ColorScheme",
    "PhysicsType",
    "InterpolationType",
    
    # Utility types
    "Color",
    "Vector3"
]

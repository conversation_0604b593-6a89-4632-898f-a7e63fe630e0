"""
Schema migration utilities for converting between v1 and v2 specifications.

This module provides functions to migrate specifications between different schema versions,
ensuring backward compatibility and smooth transitions.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..v1.models import ModelSpecification as ModelSpecificationV1
from .models import ModelSpecificationV2, ModelInfo, SceneSettings, Object3D, Transform, Vector3

logger = logging.getLogger(__name__)


class SchemaMigrationError(Exception):
    """Exception raised during schema migration."""
    pass


def migrate_v1_to_v2(v1_spec: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate a v1.x.x specification to v2.0.0 format.
    
    Args:
        v1_spec: Dictionary containing v1 specification
        
    Returns:
        Dictionary containing v2 specification
        
    Raises:
        SchemaMigrationError: If migration fails
    """
    try:
        logger.info("Starting migration from v1 to v2")
        
        # Validate input is v1 format
        if not v1_spec.get('schema_version', '').startswith('v1.'):
            raise SchemaMigrationError(f"Input schema version {v1_spec.get('schema_version')} is not v1.x.x")
        
        # Create v2 specification structure
        v2_spec = {
            "schema_version": "v2.0.0",
            "model_info": _migrate_model_info(v1_spec.get('model_info', {})),
            "scene_settings": _migrate_scene_settings(v1_spec.get('scene_settings', {})),
            "objects": _migrate_objects(v1_spec.get('objects', [])),
            "groups": [],  # New in v2, empty by default
            "mcp_structures": []  # New in v2, empty by default
        }
        
        # Add optional lighting if needed
        if _should_add_default_lighting(v1_spec):
            v2_spec["lighting"] = _create_default_lighting()
        
        logger.info("Successfully migrated v1 to v2")
        return v2_spec
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise SchemaMigrationError(f"Failed to migrate v1 to v2: {e}")


def migrate_v2_to_v1(v2_spec: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate a v2.x.x specification to v1.0.0 format (downgrade).
    
    Args:
        v2_spec: Dictionary containing v2 specification
        
    Returns:
        Dictionary containing v1 specification
        
    Raises:
        SchemaMigrationError: If migration fails
    """
    try:
        logger.info("Starting migration from v2 to v1")
        
        # Validate input is v2 format
        if not v2_spec.get('schema_version', '').startswith('v2.'):
            raise SchemaMigrationError(f"Input schema version {v2_spec.get('schema_version')} is not v2.x.x")
        
        # Create v1 specification structure
        v1_spec = {
            "schema_version": "v1.0.0",
            "model_info": _downgrade_model_info(v2_spec.get('model_info', {})),
            "scene_settings": _downgrade_scene_settings(v2_spec.get('scene_settings', {})),
            "objects": _downgrade_objects(v2_spec.get('objects', []))
        }
        
        # Log warnings for lost features
        _log_downgrade_warnings(v2_spec)
        
        logger.info("Successfully migrated v2 to v1")
        return v1_spec
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise SchemaMigrationError(f"Failed to migrate v2 to v1: {e}")


def _migrate_model_info(v1_model_info: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate model_info from v1 to v2 format."""
    v2_model_info = {
        "name": v1_model_info.get("name", "Migrated Model"),
        "description": v1_model_info.get("description", "Migrated from v1 specification"),
        "created_at": v1_model_info.get("created_at", datetime.now().isoformat()),
        "tags": v1_model_info.get("tags", [])
    }
    
    # Add new v2 fields with defaults
    v2_model_info["complexity_level"] = "basic"  # Default for migrated models
    v2_model_info["model_type"] = "standard"     # Default for migrated models
    
    return v2_model_info


def _migrate_scene_settings(v1_scene_settings: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate scene_settings from v1 to v2 format."""
    v2_scene_settings = {
        "units": v1_scene_settings.get("units", "meters")
    }
    
    # Migrate background_color if present
    if "background_color" in v1_scene_settings:
        v2_scene_settings["background_color"] = v1_scene_settings["background_color"]
    
    return v2_scene_settings


def _migrate_objects(v1_objects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Migrate objects from v1 to v2 format."""
    v2_objects = []
    
    for v1_obj in v1_objects:
        v2_obj = {
            "id": v1_obj.get("id", f"migrated_obj_{len(v2_objects)}"),
            "name": v1_obj.get("name", f"Migrated Object {len(v2_objects)}"),
            "geometry": _migrate_geometry(v1_obj.get("geometry", {})),
            "visible": v1_obj.get("visible", True)
        }
        
        # Migrate optional fields
        if "transform" in v1_obj:
            v2_obj["transform"] = v1_obj["transform"]
        
        if "material" in v1_obj:
            v2_obj["material"] = _migrate_material(v1_obj["material"])
        
        # Add new v2 fields as empty/default
        v2_obj["modifiers"] = []  # New in v2
        
        v2_objects.append(v2_obj)
    
    return v2_objects


def _migrate_geometry(v1_geometry: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate geometry from v1 to v2 format."""
    # Geometry structure is mostly compatible, just pass through
    return v1_geometry


def _migrate_material(v1_material: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate material from v1 to v2 format."""
    # Material structure is mostly compatible, just pass through
    # v2 has more material types, but v1 materials are still valid
    return v1_material


def _should_add_default_lighting(v1_spec: Dict[str, Any]) -> bool:
    """Determine if default lighting should be added during migration."""
    # Add default lighting if the scene seems to need it
    # This is a heuristic - could be made more sophisticated
    return len(v1_spec.get('objects', [])) > 0


def _create_default_lighting() -> Dict[str, Any]:
    """Create default lighting configuration for migrated scenes."""
    return {
        "lights": [
            {
                "id": "default_sun",
                "type": "sun",
                "name": "Default Sun Light",
                "energy": 5.0,
                "transform": {
                    "rotation": {
                        "x": 0.785398,  # 45 degrees
                        "y": 0.0,
                        "z": 0.785398   # 45 degrees
                    }
                }
            }
        ]
    }


def _downgrade_model_info(v2_model_info: Dict[str, Any]) -> Dict[str, Any]:
    """Downgrade model_info from v2 to v1 format."""
    v1_model_info = {
        "name": v2_model_info.get("name", "Downgraded Model"),
        "description": v2_model_info.get("description", "Downgraded from v2 specification"),
        "created_at": v2_model_info.get("created_at", datetime.now().isoformat()),
        "tags": v2_model_info.get("tags", [])
    }
    
    # v2-specific fields are dropped (complexity_level, model_type)
    return v1_model_info


def _downgrade_scene_settings(v2_scene_settings: Dict[str, Any]) -> Dict[str, Any]:
    """Downgrade scene_settings from v2 to v1 format."""
    v1_scene_settings = {
        "units": v2_scene_settings.get("units", "meters")
    }
    
    # Migrate background_color if present
    if "background_color" in v2_scene_settings:
        v1_scene_settings["background_color"] = v2_scene_settings["background_color"]
    
    # v2-specific fields are dropped (environment)
    return v1_scene_settings


def _downgrade_objects(v2_objects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Downgrade objects from v2 to v1 format."""
    v1_objects = []
    
    for v2_obj in v2_objects:
        v1_obj = {
            "id": v2_obj.get("id", f"downgraded_obj_{len(v1_objects)}"),
            "name": v2_obj.get("name", f"Downgraded Object {len(v1_objects)}"),
            "geometry": v2_obj.get("geometry", {}),
            "visible": v2_obj.get("visible", True)
        }
        
        # Migrate compatible fields
        if "transform" in v2_obj:
            v1_obj["transform"] = v2_obj["transform"]
        
        if "material" in v2_obj:
            v1_obj["material"] = _downgrade_material(v2_obj["material"])
        
        # v2-specific fields are dropped (modifiers, parent_id, physics, animation)
        v1_objects.append(v1_obj)
    
    return v1_objects


def _downgrade_material(v2_material: Dict[str, Any]) -> Dict[str, Any]:
    """Downgrade material from v2 to v1 format."""
    material_type = v2_material.get("type", "basic")
    
    # Convert v2-only material types to compatible v1 types
    if material_type in ["glass", "emission", "subsurface", "toon", "principled"]:
        # Convert to PBR with approximated properties
        return {
            "type": "pbr",
            "name": v2_material.get("name", "Converted Material"),
            "color": v2_material.get("color", {"r": 0.8, "g": 0.8, "b": 0.8, "a": 1.0}),
            "metallic": v2_material.get("metallic", 0.0),
            "roughness": v2_material.get("roughness", 0.5)
        }
    
    # Basic and PBR materials are compatible
    return v2_material


def _log_downgrade_warnings(v2_spec: Dict[str, Any]) -> None:
    """Log warnings about features that will be lost during downgrade."""
    warnings = []
    
    if v2_spec.get("lighting"):
        warnings.append("Lighting configuration will be lost")
    
    if v2_spec.get("groups"):
        warnings.append("Object groups will be lost")
    
    if v2_spec.get("mcp_structures"):
        warnings.append("MCP structures will be lost")
    
    for obj in v2_spec.get("objects", []):
        if obj.get("modifiers"):
            warnings.append(f"Modifiers on object {obj.get('id')} will be lost")
        if obj.get("physics"):
            warnings.append(f"Physics properties on object {obj.get('id')} will be lost")
        if obj.get("animation"):
            warnings.append(f"Animation data on object {obj.get('id')} will be lost")
        if obj.get("parent_id"):
            warnings.append(f"Parent relationship for object {obj.get('id')} will be lost")
    
    if warnings:
        logger.warning("The following features will be lost during v2 to v1 downgrade:")
        for warning in warnings:
            logger.warning(f"  - {warning}")


def validate_migration(original_spec: Dict[str, Any], migrated_spec: Dict[str, Any]) -> bool:
    """
    Validate that a migration preserved essential data.
    
    Args:
        original_spec: Original specification
        migrated_spec: Migrated specification
        
    Returns:
        True if migration is valid, False otherwise
    """
    try:
        # Check that basic structure is preserved
        if len(original_spec.get('objects', [])) != len(migrated_spec.get('objects', [])):
            logger.error("Object count mismatch after migration")
            return False
        
        # Check that model info is preserved
        orig_info = original_spec.get('model_info', {})
        migr_info = migrated_spec.get('model_info', {})
        
        if orig_info.get('name') != migr_info.get('name'):
            logger.error("Model name mismatch after migration")
            return False
        
        logger.info("Migration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Migration validation failed: {e}")
        return False

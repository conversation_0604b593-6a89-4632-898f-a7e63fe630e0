"""
Pydantic models for v2.0.0 3D model specification validation.

This module provides comprehensive Pydantic models that correspond to the v2 JSON Schema,
enabling runtime validation and type checking for advanced 3D model specifications.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Union, Literal, Dict, Any
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
import re


# Enums
class UnitsEnum(str, Enum):
    """Supported units for scene measurements."""
    METERS = "meters"
    CENTIMETERS = "centimeters"
    MILLIMETERS = "millimeters"
    INCHES = "inches"
    FEET = "feet"
    ANGSTROMS = "angstroms"


class GeometryTypeEnum(str, Enum):
    """Supported geometry types including complex shapes."""
    # Basic geometries
    CUBE = "cube"
    SPHERE = "sphere"
    CYLINDER = "cylinder"
    PLANE = "plane"
    CONE = "cone"
    
    # Complex geometries
    TORUS = "torus"
    PYRAMID = "pyramid"
    PRISM = "prism"
    ELLIPSOID = "ellipsoid"
    TETRAHEDRON = "tetrahedron"
    OCTAHEDRON = "octahedron"
    DODECAHEDRON = "dodecahedron"
    ICOSAHEDRON = "icosahedron"
    
    # Advanced geometries
    MESH = "mesh"
    COMPOSITE = "composite"


class MaterialTypeEnum(str, Enum):
    """Supported material types."""
    BASIC = "basic"
    PBR = "pbr"
    GLASS = "glass"
    EMISSION = "emission"
    SUBSURFACE = "subsurface"
    TOON = "toon"
    PRINCIPLED = "principled"


class ModifierTypeEnum(str, Enum):
    """Supported modifier types."""
    ARRAY = "array"
    MIRROR = "mirror"
    SOLIDIFY = "solidify"
    BEVEL = "bevel"
    SUBDIVISION_SURFACE = "subdivision_surface"
    DISPLACEMENT = "displacement"
    WAVE = "wave"
    SCREW = "screw"


class LightTypeEnum(str, Enum):
    """Supported light types."""
    POINT = "point"
    SUN = "sun"
    SPOT = "spot"
    AREA = "area"


class ComplexityLevel(str, Enum):
    """Model complexity levels."""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class ModelType(str, Enum):
    """Model structure types."""
    STANDARD = "standard"
    MOLECULAR = "molecular"
    PROTEIN = "protein"
    COMPOSITE = "composite"


class BondType(str, Enum):
    """Chemical bond types."""
    SINGLE = "single"
    DOUBLE = "double"
    TRIPLE = "triple"
    AROMATIC = "aromatic"


class RepresentationStyle(str, Enum):
    """Molecular/protein representation styles."""
    BALL_AND_STICK = "ball_and_stick"
    SPACE_FILLING = "space_filling"
    WIREFRAME = "wireframe"
    CARTOON = "cartoon"
    RIBBON = "ribbon"
    SURFACE = "surface"


class ColorScheme(str, Enum):
    """Protein coloring schemes."""
    CHAIN = "chain"
    RESIDUE = "residue"
    SECONDARY_STRUCTURE = "secondary_structure"
    CUSTOM = "custom"


class PhysicsType(str, Enum):
    """Physics body types."""
    NONE = "none"
    STATIC = "static"
    DYNAMIC = "dynamic"
    KINEMATIC = "kinematic"


class InterpolationType(str, Enum):
    """Animation interpolation types."""
    LINEAR = "linear"
    BEZIER = "bezier"
    CONSTANT = "constant"


# Base utility models
class Color(BaseModel):
    """RGBA color specification."""
    model_config = ConfigDict(extra="forbid")
    
    r: float = Field(..., ge=0.0, le=1.0, description="Red component (0.0-1.0)")
    g: float = Field(..., ge=0.0, le=1.0, description="Green component (0.0-1.0)")
    b: float = Field(..., ge=0.0, le=1.0, description="Blue component (0.0-1.0)")
    a: float = Field(1.0, ge=0.0, le=1.0, description="Alpha component (0.0-1.0)")


class Vector3(BaseModel):
    """3D vector specification."""
    model_config = ConfigDict(extra="forbid")
    
    x: float = Field(..., description="X coordinate")
    y: float = Field(..., description="Y coordinate")
    z: float = Field(..., description="Z coordinate")


class Transform(BaseModel):
    """3D transformation specification."""
    model_config = ConfigDict(extra="forbid")
    
    position: Optional[Vector3] = Field(None, description="Position in 3D space")
    rotation: Optional[Vector3] = Field(None, description="Rotation in Euler angles (radians)")
    scale: Optional[Vector3] = Field(None, description="Scale factors for each axis")


# Environment and lighting models
class Environment(BaseModel):
    """Environment settings."""
    model_config = ConfigDict(extra="forbid")
    
    hdri_path: Optional[str] = Field(None, description="Path to HDRI environment texture")
    strength: float = Field(1.0, ge=0.0, le=10.0, description="Environment lighting strength")
    rotation: float = Field(0.0, ge=0.0, le=6.283185307179586, description="Environment rotation in radians")


class Light(BaseModel):
    """Base light specification."""
    model_config = ConfigDict(extra="forbid")
    
    id: str = Field(..., pattern=r"^[a-zA-Z0-9_-]+$", min_length=1, max_length=50, description="Unique identifier")
    type: LightTypeEnum = Field(..., description="Type of light")
    name: str = Field(..., min_length=1, max_length=100, description="Human-readable name")
    transform: Optional[Transform] = Field(None, description="Light position and orientation")
    energy: float = Field(10.0, ge=0.0, le=1000.0, description="Light energy/strength")
    color: Optional[Color] = Field(None, description="Light color")


class PointLight(Light):
    """Point light specification."""
    type: Literal[LightTypeEnum.POINT] = Field(LightTypeEnum.POINT, description="Light type")


class SunLight(Light):
    """Sun light specification."""
    type: Literal[LightTypeEnum.SUN] = Field(LightTypeEnum.SUN, description="Light type")


class SpotLight(Light):
    """Spot light specification."""
    type: Literal[LightTypeEnum.SPOT] = Field(LightTypeEnum.SPOT, description="Light type")
    spot_size: Optional[float] = Field(None, ge=0.0, le=3.141592653589793, description="Spot cone size in radians")
    spot_blend: Optional[float] = Field(None, ge=0.0, le=1.0, description="Spot edge softness")


class AreaLight(Light):
    """Area light specification."""
    type: Literal[LightTypeEnum.AREA] = Field(LightTypeEnum.AREA, description="Light type")
    size: Optional[float] = Field(None, ge=0.0, le=100.0, description="Light size")


# Geometry models
class BaseGeometry(BaseModel):
    """Base class for all geometry types."""
    model_config = ConfigDict(extra="forbid")
    
    type: GeometryTypeEnum = Field(..., description="Type of geometry")


class CubeGeometry(BaseGeometry):
    """Cube geometry specification."""
    type: Literal[GeometryTypeEnum.CUBE] = Field(GeometryTypeEnum.CUBE, description="Geometry type")
    size: float = Field(2.0, gt=0.001, description="Size of the cube (edge length)")


class SphereGeometry(BaseGeometry):
    """Sphere geometry specification."""
    type: Literal[GeometryTypeEnum.SPHERE] = Field(GeometryTypeEnum.SPHERE, description="Geometry type")
    radius: float = Field(1.0, gt=0.001, description="Radius of the sphere")
    subdivisions: int = Field(4, ge=3, le=10, description="Number of subdivisions for sphere mesh")


class CylinderGeometry(BaseGeometry):
    """Cylinder geometry specification."""
    type: Literal[GeometryTypeEnum.CYLINDER] = Field(GeometryTypeEnum.CYLINDER, description="Geometry type")
    radius: float = Field(1.0, gt=0.001, description="Radius of the cylinder")
    height: float = Field(2.0, gt=0.001, description="Height of the cylinder")
    vertices: int = Field(32, ge=3, le=64, description="Number of vertices around the cylinder")


class PlaneGeometry(BaseGeometry):
    """Plane geometry specification."""
    type: Literal[GeometryTypeEnum.PLANE] = Field(GeometryTypeEnum.PLANE, description="Geometry type")
    size: float = Field(2.0, gt=0.001, description="Size of the plane (edge length)")


class ConeGeometry(BaseGeometry):
    """Cone geometry specification."""
    type: Literal[GeometryTypeEnum.CONE] = Field(GeometryTypeEnum.CONE, description="Geometry type")
    radius: float = Field(1.0, gt=0.001, description="Radius of the cone base")
    height: float = Field(2.0, gt=0.001, description="Height of the cone")
    vertices: int = Field(32, ge=3, le=64, description="Number of vertices around the cone base")


class TorusGeometry(BaseGeometry):
    """Torus geometry specification."""
    type: Literal[GeometryTypeEnum.TORUS] = Field(GeometryTypeEnum.TORUS, description="Geometry type")
    major_radius: float = Field(1.0, gt=0.001, description="Major radius of the torus")
    minor_radius: float = Field(0.25, gt=0.001, description="Minor radius of the torus")
    major_segments: int = Field(48, ge=3, le=256, description="Number of segments around the major radius")
    minor_segments: int = Field(12, ge=3, le=64, description="Number of segments around the minor radius")


# Union type for all geometry types
Geometry = Union[
    CubeGeometry, SphereGeometry, CylinderGeometry, PlaneGeometry,
    ConeGeometry, TorusGeometry
]


# Material models
class BaseMaterial(BaseModel):
    """Base class for all material types."""
    model_config = ConfigDict(extra="forbid")

    type: MaterialTypeEnum = Field(..., description="Type of material")
    name: str = Field(..., min_length=1, max_length=100, description="Name of the material")
    color: Optional[Color] = Field(None, description="Base color of the material")


class BasicMaterial(BaseMaterial):
    """Basic color-based material."""
    type: Literal[MaterialTypeEnum.BASIC] = Field(MaterialTypeEnum.BASIC, description="Material type")


class PBRMaterial(BaseMaterial):
    """Physically-based rendering material."""
    type: Literal[MaterialTypeEnum.PBR] = Field(MaterialTypeEnum.PBR, description="Material type")
    metallic: float = Field(0.0, ge=0.0, le=1.0, description="Metallic factor")
    roughness: float = Field(0.5, ge=0.0, le=1.0, description="Roughness factor")
    normal_map: Optional[str] = Field(None, description="Path to normal map texture")
    bump_map: Optional[str] = Field(None, description="Path to bump map texture")
    texture_scale: float = Field(1.0, ge=0.001, le=100.0, description="Texture coordinate scale")


class GlassMaterial(BaseMaterial):
    """Glass material with transmission properties."""
    type: Literal[MaterialTypeEnum.GLASS] = Field(MaterialTypeEnum.GLASS, description="Material type")
    transmission: float = Field(1.0, ge=0.0, le=1.0, description="Transmission factor")
    ior: float = Field(1.45, ge=1.0, le=3.0, description="Index of refraction")
    roughness: float = Field(0.0, ge=0.0, le=1.0, description="Surface roughness")


class EmissionMaterial(BaseMaterial):
    """Emission material for glowing objects."""
    type: Literal[MaterialTypeEnum.EMISSION] = Field(MaterialTypeEnum.EMISSION, description="Material type")
    emission: Optional[Color] = Field(None, description="Emission color")
    emission_strength: float = Field(1.0, ge=0.0, le=100.0, description="Emission strength")


class SubsurfaceMaterial(BaseMaterial):
    """Subsurface scattering material."""
    type: Literal[MaterialTypeEnum.SUBSURFACE] = Field(MaterialTypeEnum.SUBSURFACE, description="Material type")
    subsurface: float = Field(0.1, ge=0.0, le=1.0, description="Subsurface scattering factor")
    subsurface_radius: Optional[Color] = Field(None, description="Subsurface scattering radius (RGB)")


class ToonMaterial(BaseMaterial):
    """Toon/cartoon-style material."""
    type: Literal[MaterialTypeEnum.TOON] = Field(MaterialTypeEnum.TOON, description="Material type")
    toon_size: float = Field(0.5, ge=0.0, le=1.0, description="Toon shading size")
    toon_smooth: float = Field(0.0, ge=0.0, le=1.0, description="Toon shading smoothness")


class PrincipledMaterial(BaseMaterial):
    """Principled BSDF material with all properties."""
    type: Literal[MaterialTypeEnum.PRINCIPLED] = Field(MaterialTypeEnum.PRINCIPLED, description="Material type")
    metallic: float = Field(0.0, ge=0.0, le=1.0, description="Metallic factor")
    roughness: float = Field(0.5, ge=0.0, le=1.0, description="Roughness factor")
    transmission: float = Field(0.0, ge=0.0, le=1.0, description="Transmission factor")
    ior: float = Field(1.45, ge=1.0, le=3.0, description="Index of refraction")
    subsurface: float = Field(0.0, ge=0.0, le=1.0, description="Subsurface scattering factor")
    subsurface_radius: Optional[Color] = Field(None, description="Subsurface scattering radius")
    emission: Optional[Color] = Field(None, description="Emission color")
    emission_strength: float = Field(0.0, ge=0.0, le=100.0, description="Emission strength")
    normal_map: Optional[str] = Field(None, description="Path to normal map texture")
    bump_map: Optional[str] = Field(None, description="Path to bump map texture")
    texture_scale: float = Field(1.0, ge=0.001, le=100.0, description="Texture coordinate scale")


# Union type for all material types
Material = Union[
    BasicMaterial, PBRMaterial, GlassMaterial, EmissionMaterial,
    SubsurfaceMaterial, ToonMaterial, PrincipledMaterial
]


# Modifier models
class BaseModifier(BaseModel):
    """Base class for all modifier types."""
    model_config = ConfigDict(extra="forbid")

    type: ModifierTypeEnum = Field(..., description="Type of modifier")
    name: str = Field(..., min_length=1, max_length=100, description="Name of the modifier")
    enabled: bool = Field(True, description="Whether the modifier is enabled")


class ArrayModifier(BaseModifier):
    """Array modifier for duplicating objects."""
    type: Literal[ModifierTypeEnum.ARRAY] = Field(ModifierTypeEnum.ARRAY, description="Modifier type")
    count: int = Field(2, ge=1, le=1000, description="Number of array copies")
    offset: Optional[Vector3] = Field(None, description="Offset between array copies")


class MirrorModifier(BaseModifier):
    """Mirror modifier for symmetrical objects."""
    type: Literal[ModifierTypeEnum.MIRROR] = Field(ModifierTypeEnum.MIRROR, description="Modifier type")
    axis: List[str] = Field(["x"], description="Mirror axes")
    merge: bool = Field(True, description="Merge vertices at mirror plane")

    @field_validator('axis')
    @classmethod
    def validate_axis(cls, v):
        valid_axes = {"x", "y", "z"}
        if not all(axis in valid_axes for axis in v):
            raise ValueError("Axis must be one of 'x', 'y', 'z'")
        return v


class SolidifyModifier(BaseModifier):
    """Solidify modifier for adding thickness."""
    type: Literal[ModifierTypeEnum.SOLIDIFY] = Field(ModifierTypeEnum.SOLIDIFY, description="Modifier type")
    thickness: float = Field(0.1, ge=0.001, le=10.0, description="Solidify thickness")
    offset: float = Field(0.0, ge=-1.0, le=1.0, description="Solidify offset")


class BevelModifier(BaseModifier):
    """Bevel modifier for rounded edges."""
    type: Literal[ModifierTypeEnum.BEVEL] = Field(ModifierTypeEnum.BEVEL, description="Modifier type")
    width: float = Field(0.1, ge=0.001, le=10.0, description="Bevel width")
    segments: int = Field(1, ge=1, le=100, description="Number of bevel segments")


class SubdivisionSurfaceModifier(BaseModifier):
    """Subdivision surface modifier for smooth surfaces."""
    type: Literal[ModifierTypeEnum.SUBDIVISION_SURFACE] = Field(ModifierTypeEnum.SUBDIVISION_SURFACE, description="Modifier type")
    levels: int = Field(2, ge=0, le=6, description="Subdivision levels")
    render_levels: int = Field(2, ge=0, le=6, description="Render subdivision levels")


# Union type for all modifier types
Modifier = Union[
    ArrayModifier, MirrorModifier, SolidifyModifier,
    BevelModifier, SubdivisionSurfaceModifier
]


# Physics and animation models
class Physics(BaseModel):
    """Physics properties for objects."""
    model_config = ConfigDict(extra="forbid")

    type: PhysicsType = Field(PhysicsType.NONE, description="Physics body type")
    mass: float = Field(1.0, ge=0.001, le=1000.0, description="Object mass for physics simulation")
    friction: float = Field(0.5, ge=0.0, le=1.0, description="Surface friction coefficient")
    restitution: float = Field(0.0, ge=0.0, le=1.0, description="Bounce/restitution coefficient")


class Keyframe(BaseModel):
    """Animation keyframe specification."""
    model_config = ConfigDict(extra="forbid")

    frame: int = Field(..., ge=1, description="Animation frame number")
    property: str = Field(..., description="Animated property")
    value: Vector3 = Field(..., description="Property value at this frame")
    interpolation: InterpolationType = Field(InterpolationType.LINEAR, description="Interpolation method")

    @field_validator('property')
    @classmethod
    def validate_property(cls, v):
        valid_properties = {"location", "rotation", "scale"}
        if v not in valid_properties:
            raise ValueError(f"Property must be one of {valid_properties}")
        return v


class Animation(BaseModel):
    """Animation specification for objects."""
    model_config = ConfigDict(extra="forbid")

    keyframes: List[Keyframe] = Field(default_factory=list, description="Animation keyframes")


# MCP (Molecular Nodes) models
class Atom(BaseModel):
    """Atom specification for molecular structures."""
    model_config = ConfigDict(extra="forbid")

    id: str = Field(..., pattern=r"^[a-zA-Z0-9_-]+$", description="Unique atom identifier")
    element: str = Field(..., pattern=r"^[A-Z][a-z]?$", description="Chemical element symbol")
    position: Vector3 = Field(..., description="Atom position in 3D space")
    radius: Optional[float] = Field(None, ge=0.1, le=5.0, description="Atom radius for visualization")
    color: Optional[Color] = Field(None, description="Atom color override")


class Bond(BaseModel):
    """Bond specification for molecular structures."""
    model_config = ConfigDict(extra="forbid")

    atom1_id: str = Field(..., pattern=r"^[a-zA-Z0-9_-]+$", description="ID of first atom")
    atom2_id: str = Field(..., pattern=r"^[a-zA-Z0-9_-]+$", description="ID of second atom")
    bond_type: BondType = Field(..., description="Type of chemical bond")
    length: Optional[float] = Field(None, ge=0.5, le=5.0, description="Bond length override")


class MolecularStructure(BaseModel):
    """Molecular structure specification."""
    model_config = ConfigDict(extra="forbid")

    atoms: List[Atom] = Field(..., min_length=1, description="Array of atoms in the molecular structure")
    bonds: List[Bond] = Field(default_factory=list, description="Array of bonds between atoms")
    representation: RepresentationStyle = Field(RepresentationStyle.BALL_AND_STICK, description="Molecular representation style")


class ProteinStructure(BaseModel):
    """Protein structure specification."""
    model_config = ConfigDict(extra="forbid")

    pdb_file: Optional[str] = Field(None, description="Path to PDB file for protein structure")
    chain_ids: List[str] = Field(default_factory=list, description="Protein chain IDs to include")
    representation: RepresentationStyle = Field(RepresentationStyle.CARTOON, description="Protein representation style")
    color_scheme: ColorScheme = Field(ColorScheme.CHAIN, description="Protein coloring scheme")
    show_sidechains: bool = Field(False, description="Whether to show amino acid side chains")
    show_backbone: bool = Field(True, description="Whether to show protein backbone")

    @field_validator('chain_ids')
    @classmethod
    def validate_chain_ids(cls, v):
        for chain_id in v:
            if not re.match(r"^[A-Z]$", chain_id):
                raise ValueError("Chain IDs must be single uppercase letters")
        return v


class MCPStructure(BaseModel):
    """MCP (Molecular Nodes) structure specification."""
    model_config = ConfigDict(extra="forbid")

    id: str = Field(..., pattern=r"^[a-zA-Z0-9_-]+$", min_length=1, max_length=50, description="Unique identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Human-readable name")
    structure_type: str = Field(..., description="Type of MCP structure")
    transform: Optional[Transform] = Field(None, description="Structure transformation")

    # Discriminated union based on structure_type
    molecular: Optional[MolecularStructure] = Field(None, description="Molecular structure data")
    protein: Optional[ProteinStructure] = Field(None, description="Protein structure data")

    @model_validator(mode='after')
    def validate_structure_data(self):
        if self.structure_type == "molecular" and not self.molecular:
            raise ValueError("Molecular structure data required for molecular type")
        elif self.structure_type == "protein" and not self.protein:
            raise ValueError("Protein structure data required for protein type")
        return self


# Main object and structure models
class Object3D(BaseModel):
    """3D object specification."""
    model_config = ConfigDict(extra="forbid")

    id: str = Field(..., pattern=r"^[a-zA-Z0-9_-]+$", min_length=1, max_length=50, description="Unique identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Human-readable name")
    geometry: Geometry = Field(..., description="Geometric properties")
    transform: Optional[Transform] = Field(None, description="Transformation properties")
    material: Optional[Material] = Field(None, description="Material properties")
    modifiers: List[Modifier] = Field(default_factory=list, description="Array of modifiers")
    visible: bool = Field(True, description="Whether the object is visible")
    parent_id: Optional[str] = Field(None, pattern=r"^[a-zA-Z0-9_-]+$", description="ID of parent object")
    physics: Optional[Physics] = Field(None, description="Physics properties")
    animation: Optional[Animation] = Field(None, description="Animation properties")


class Group(BaseModel):
    """Object group specification."""
    model_config = ConfigDict(extra="forbid")

    id: str = Field(..., pattern=r"^[a-zA-Z0-9_-]+$", min_length=1, max_length=50, description="Unique identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Human-readable name")
    object_ids: List[str] = Field(..., min_length=1, description="Array of object IDs in this group")
    transform: Optional[Transform] = Field(None, description="Group-level transformation")
    visible: bool = Field(True, description="Whether the group is visible")

    @field_validator('object_ids')
    @classmethod
    def validate_object_ids(cls, v):
        for obj_id in v:
            if not re.match(r"^[a-zA-Z0-9_-]+$", obj_id):
                raise ValueError("Object IDs must contain only alphanumeric characters, underscores, and hyphens")
        return v


# Model information and scene settings
class ModelInfo(BaseModel):
    """Model metadata and information."""
    model_config = ConfigDict(extra="forbid")

    name: str = Field(..., min_length=1, max_length=100, description="Name of the 3D model")
    description: str = Field(..., max_length=1000, description="Description of the 3D model")
    created_at: Optional[datetime] = Field(None, description="When the model spec was created")
    tags: List[str] = Field(default_factory=list, max_length=20, description="Tags for categorizing the model")
    complexity_level: Optional[ComplexityLevel] = Field(None, description="Complexity level of the model")
    model_type: Optional[ModelType] = Field(None, description="Type of model structure")

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        for tag in v:
            if not (1 <= len(tag) <= 50):
                raise ValueError("Each tag must be between 1 and 50 characters")
        return v


class Lighting(BaseModel):
    """Scene lighting specification."""
    model_config = ConfigDict(extra="forbid")

    lights: List[Light] = Field(default_factory=list, description="Array of lights in the scene")
    global_illumination: Optional[Dict[str, Any]] = Field(None, description="Global illumination settings")


class SceneSettings(BaseModel):
    """Scene-level settings and configuration."""
    model_config = ConfigDict(extra="forbid")

    units: UnitsEnum = Field(UnitsEnum.METERS, description="Units used for measurements")
    background_color: Optional[Color] = Field(None, description="Background color of the scene")
    environment: Optional[Environment] = Field(None, description="Environment settings")


# Main model specification
class ModelSpecificationV2(BaseModel):
    """Complete v2.0.0 3D model specification."""
    model_config = ConfigDict(extra="forbid")

    schema_version: str = Field(..., pattern=r"^v2\.[0-9]+\.[0-9]+$", description="Schema version")
    model_info: ModelInfo = Field(..., description="Model metadata and information")
    scene_settings: Optional[SceneSettings] = Field(None, description="Scene-level settings")
    lighting: Optional[Lighting] = Field(None, description="Scene lighting configuration")
    objects: List[Object3D] = Field(..., min_length=1, description="Array of 3D objects")
    groups: List[Group] = Field(default_factory=list, description="Array of object groups")
    mcp_structures: List[MCPStructure] = Field(default_factory=list, description="Array of MCP structures")

    @field_validator('schema_version')
    @classmethod
    def validate_schema_version(cls, v):
        if not v.startswith('v2.'):
            raise ValueError("Schema version must start with 'v2.'")
        return v

    @model_validator(mode='after')
    def validate_object_references(self):
        """Validate that all object references are valid."""
        object_ids = {obj.id for obj in self.objects}

        # Check parent references
        for obj in self.objects:
            if obj.parent_id and obj.parent_id not in object_ids:
                raise ValueError(f"Object {obj.id} references non-existent parent {obj.parent_id}")

        # Check group references
        for group in self.groups:
            for obj_id in group.object_ids:
                if obj_id not in object_ids:
                    raise ValueError(f"Group {group.id} references non-existent object {obj_id}")

        return self

    @model_validator(mode='after')
    def validate_unique_ids(self):
        """Validate that all IDs are unique across objects, groups, and MCP structures."""
        all_ids = []

        # Collect all IDs
        all_ids.extend([obj.id for obj in self.objects])
        all_ids.extend([group.id for group in self.groups])
        all_ids.extend([mcp.id for mcp in self.mcp_structures])

        # Check for duplicates
        if len(all_ids) != len(set(all_ids)):
            raise ValueError("All IDs must be unique across objects, groups, and MCP structures")

        return self

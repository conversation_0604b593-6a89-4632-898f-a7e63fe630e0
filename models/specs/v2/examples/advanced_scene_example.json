{"schema_version": "v2.0.0", "model_info": {"name": "Advanced Glass and Metal Scene", "description": "A complex scene demonstrating advanced materials, modifiers, and lighting in v2 schema", "created_at": "2025-01-19T15:30:00Z", "tags": ["advanced", "glass", "metal", "lighting", "modifiers"], "complexity_level": "advanced", "model_type": "composite"}, "scene_settings": {"units": "meters", "background_color": {"r": 0.05, "g": 0.05, "b": 0.1, "a": 1.0}, "environment": {"hdri_path": "/textures/studio_lighting.hdr", "strength": 1.2, "rotation": 0.785398}}, "lighting": {"lights": [{"id": "main_sun", "type": "sun", "name": "Main Sun Light", "energy": 5.0, "color": {"r": 1.0, "g": 0.95, "b": 0.8, "a": 1.0}, "transform": {"rotation": {"x": 0.785398, "y": 0.0, "z": 0.785398}}}, {"id": "fill_light", "type": "area", "name": "Fill Light", "energy": 2.0, "size": 2.0, "color": {"r": 0.8, "g": 0.9, "b": 1.0, "a": 1.0}, "transform": {"position": {"x": -3.0, "y": 3.0, "z": 2.0}, "rotation": {"x": 0.523599, "y": -0.785398, "z": 0.0}}}, {"id": "rim_light", "type": "spot", "name": "<PERSON><PERSON>", "energy": 8.0, "spot_size": 0.785398, "spot_blend": 0.2, "color": {"r": 1.0, "g": 0.8, "b": 0.6, "a": 1.0}, "transform": {"position": {"x": 4.0, "y": -2.0, "z": 3.0}, "rotation": {"x": 0.349066, "y": 1.047198, "z": 0.0}}}], "global_illumination": {"enabled": true, "strength": 1.0}}, "objects": [{"id": "glass_sphere", "name": "Glass Sphere", "geometry": {"type": "sphere", "radius": 1.0, "subdivisions": 6}, "transform": {"position": {"x": -2.0, "y": 0.0, "z": 1.0}, "scale": {"x": 1.2, "y": 1.2, "z": 1.2}}, "material": {"type": "glass", "name": "Clear Glass", "color": {"r": 0.9, "g": 0.95, "b": 1.0, "a": 0.1}, "transmission": 0.95, "ior": 1.45, "roughness": 0.0}, "modifiers": [{"type": "subdivision_surface", "name": "Smooth Glass", "levels": 3, "render_levels": 4, "enabled": true}], "physics": {"type": "dynamic", "mass": 0.5, "friction": 0.1, "restitution": 0.9}, "visible": true}, {"id": "metal_torus", "name": "Metal Torus", "geometry": {"type": "torus", "major_radius": 1.5, "minor_radius": 0.3, "major_segments": 64, "minor_segments": 16}, "transform": {"position": {"x": 2.0, "y": 0.0, "z": 0.3}, "rotation": {"x": 1.570796, "y": 0.0, "z": 0.0}}, "material": {"type": "principled", "name": "Brushed Steel", "color": {"r": 0.7, "g": 0.7, "b": 0.75, "a": 1.0}, "metallic": 0.9, "roughness": 0.3, "normal_map": "/textures/brushed_metal_normal.png", "texture_scale": 2.0}, "modifiers": [{"type": "bevel", "name": "<PERSON>", "width": 0.05, "segments": 3, "enabled": true}, {"type": "array", "name": "Circular Array", "count": 3, "offset": {"x": 0.0, "y": 0.0, "z": 1.0}, "enabled": true}], "physics": {"type": "dynamic", "mass": 3.0, "friction": 0.8, "restitution": 0.1}, "visible": true}, {"id": "emission_cube", "name": "Glowing Cube", "geometry": {"type": "cube", "size": 1.0}, "transform": {"position": {"x": 0.0, "y": 2.5, "z": 2.0}, "rotation": {"x": 0.0, "y": 0.785398, "z": 0.0}}, "material": {"type": "emission", "name": "Orange Glow", "color": {"r": 1.0, "g": 0.5, "b": 0.1, "a": 1.0}, "emission": {"r": 1.0, "g": 0.4, "b": 0.05, "a": 1.0}, "emission_strength": 5.0}, "modifiers": [{"type": "solidify", "name": "Shell", "thickness": 0.1, "offset": -0.5, "enabled": true}, {"type": "bevel", "name": "Rounded Edges", "width": 0.1, "segments": 2, "enabled": true}], "animation": {"keyframes": [{"frame": 1, "property": "rotation", "value": {"x": 0.0, "y": 0.0, "z": 0.0}, "interpolation": "linear"}, {"frame": 120, "property": "rotation", "value": {"x": 0.0, "y": 6.283185, "z": 0.0}, "interpolation": "linear"}]}, "visible": true}, {"id": "ground_plane", "name": "Ground Plane", "geometry": {"type": "plane", "size": 10.0}, "transform": {"position": {"x": 0.0, "y": 0.0, "z": -1.0}}, "material": {"type": "pbr", "name": "Concrete Floor", "color": {"r": 0.4, "g": 0.4, "b": 0.45, "a": 1.0}, "metallic": 0.0, "roughness": 0.8, "normal_map": "/textures/concrete_normal.png", "bump_map": "/textures/concrete_bump.png", "texture_scale": 4.0}, "physics": {"type": "static", "mass": 0.0, "friction": 0.9, "restitution": 0.0}, "visible": true}], "groups": [{"id": "dynamic_objects", "name": "Dynamic Objects", "object_ids": ["glass_sphere", "metal_torus", "emission_cube"], "visible": true}, {"id": "metallic_objects", "name": "Metallic Objects", "object_ids": ["metal_torus"], "visible": true}]}
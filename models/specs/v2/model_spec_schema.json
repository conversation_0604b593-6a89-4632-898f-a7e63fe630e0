{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://blender-ai-agent.com/schemas/v2/model_spec_schema.json", "title": "Advanced 3D Model Specification", "description": "JSON Schema for advanced 3D model specifications supporting complex materials, modifiers, lighting, and MCP structures", "version": "2.0.0", "type": "object", "required": ["schema_version", "model_info", "objects"], "properties": {"schema_version": {"type": "string", "pattern": "^v2\\.[0-9]+\\.[0-9]+$", "description": "Version of the schema being used", "examples": ["v2.0.0", "v2.1.0"]}, "model_info": {"type": "object", "required": ["name", "description"], "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Name of the 3D model"}, "description": {"type": "string", "maxLength": 1000, "description": "Description of the 3D model"}, "created_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when the model spec was created"}, "tags": {"type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 50}, "maxItems": 20, "description": "Tags for categorizing the model"}, "complexity_level": {"type": "string", "enum": ["basic", "intermediate", "advanced", "expert"], "description": "Complexity level of the model"}, "model_type": {"type": "string", "enum": ["standard", "molecular", "protein", "composite"], "description": "Type of model structure"}}}, "scene_settings": {"type": "object", "properties": {"units": {"type": "string", "enum": ["meters", "centimeters", "millimeters", "inches", "feet", "angstroms"], "default": "meters", "description": "Units used for measurements in the scene"}, "background_color": {"$ref": "#/definitions/color", "description": "Background color of the scene"}, "environment": {"$ref": "#/definitions/environment", "description": "Environment settings for the scene"}}}, "lighting": {"type": "object", "properties": {"lights": {"type": "array", "items": {"$ref": "#/definitions/light"}, "description": "Array of lights in the scene"}, "global_illumination": {"type": "object", "properties": {"enabled": {"type": "boolean", "default": false}, "strength": {"type": "number", "minimum": 0.0, "maximum": 10.0, "default": 1.0}}}}}, "objects": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/object"}, "description": "Array of 3D objects in the model"}, "groups": {"type": "array", "items": {"$ref": "#/definitions/group"}, "description": "Array of object groups for hierarchical organization"}, "mcp_structures": {"type": "array", "items": {"$ref": "#/definitions/mcp_structure"}, "description": "Array of Molecular Nodes (MCP) structures"}}, "definitions": {"color": {"type": "object", "required": ["r", "g", "b"], "properties": {"r": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "Red component (0.0-1.0)"}, "g": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "Green component (0.0-1.0)"}, "b": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "Blue component (0.0-1.0)"}, "a": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 1.0, "description": "Alpha component (0.0-1.0)"}}}, "vector3": {"type": "object", "required": ["x", "y", "z"], "properties": {"x": {"type": "number", "description": "X coordinate"}, "y": {"type": "number", "description": "Y coordinate"}, "z": {"type": "number", "description": "Z coordinate"}}}, "transform": {"type": "object", "properties": {"position": {"$ref": "#/definitions/vector3", "description": "Position in 3D space"}, "rotation": {"$ref": "#/definitions/vector3", "description": "Rotation in Euler angles (radians)"}, "scale": {"$ref": "#/definitions/vector3", "description": "Scale factors for each axis"}}}, "environment": {"type": "object", "properties": {"hdri_path": {"type": "string", "description": "Path to HDRI environment texture"}, "strength": {"type": "number", "minimum": 0.0, "maximum": 10.0, "default": 1.0, "description": "Environment lighting strength"}, "rotation": {"type": "number", "minimum": 0.0, "maximum": 6.283185307179586, "default": 0.0, "description": "Environment rotation in radians"}}}, "light": {"type": "object", "required": ["id", "type"], "properties": {"id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "minLength": 1, "maxLength": 50, "description": "Unique identifier for the light"}, "type": {"type": "string", "enum": ["point", "sun", "spot", "area"], "description": "Type of light"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable name for the light"}, "transform": {"$ref": "#/definitions/transform", "description": "Light position and orientation"}, "energy": {"type": "number", "minimum": 0.0, "maximum": 1000.0, "default": 10.0, "description": "Light energy/strength"}, "color": {"$ref": "#/definitions/color", "description": "Light color"}, "spot_size": {"type": "number", "minimum": 0.0, "maximum": 3.141592653589793, "description": "Spot light cone size in radians (spot lights only)"}, "spot_blend": {"type": "number", "minimum": 0.0, "maximum": 1.0, "description": "Spot light edge softness (spot lights only)"}, "size": {"type": "number", "minimum": 0.0, "maximum": 100.0, "description": "Light size for area lights"}}}, "geometry": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["cube", "sphere", "cylinder", "plane", "cone", "torus", "pyramid", "prism", "ellipsoid", "tetrahedron", "octahedron", "dodecahedron", "icosahedron", "mesh", "composite"], "description": "Type of geometry"}}, "allOf": [{"if": {"properties": {"type": {"const": "cube"}}}, "then": {"properties": {"size": {"type": "number", "minimum": 0.001, "description": "Size of the cube (edge length)"}}, "required": ["size"]}}, {"if": {"properties": {"type": {"const": "sphere"}}}, "then": {"properties": {"radius": {"type": "number", "minimum": 0.001, "description": "<PERSON>dius of the sphere"}, "subdivisions": {"type": "integer", "minimum": 3, "maximum": 10, "default": 4, "description": "Number of subdivisions for sphere mesh"}}, "required": ["radius"]}}, {"if": {"properties": {"type": {"const": "torus"}}}, "then": {"properties": {"major_radius": {"type": "number", "minimum": 0.001, "description": "Major radius of the torus"}, "minor_radius": {"type": "number", "minimum": 0.001, "description": "Minor radius of the torus"}, "major_segments": {"type": "integer", "minimum": 3, "maximum": 256, "default": 48, "description": "Number of segments around the major radius"}, "minor_segments": {"type": "integer", "minimum": 3, "maximum": 64, "default": 12, "description": "Number of segments around the minor radius"}}, "required": ["major_radius", "minor_radius"]}}]}, "material": {"type": "object", "required": ["type", "name"], "properties": {"type": {"type": "string", "enum": ["basic", "pbr", "glass", "emission", "subsurface", "toon", "principled"], "description": "Type of material"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Name of the material"}, "color": {"$ref": "#/definitions/color", "description": "Base color of the material"}, "metallic": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 0.0, "description": "Metallic factor (PBR materials)"}, "roughness": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 0.5, "description": "Roughness factor (PBR materials)"}, "emission": {"$ref": "#/definitions/color", "description": "Emission color for glowing materials"}, "emission_strength": {"type": "number", "minimum": 0.0, "maximum": 100.0, "default": 1.0, "description": "Emission strength"}, "transmission": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 0.0, "description": "Transmission factor for glass materials"}, "ior": {"type": "number", "minimum": 1.0, "maximum": 3.0, "default": 1.45, "description": "Index of refraction for glass materials"}, "subsurface": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 0.0, "description": "Subsurface scattering factor"}, "subsurface_radius": {"$ref": "#/definitions/color", "description": "Subsurface scattering radius (RGB)"}, "normal_map": {"type": "string", "description": "Path to normal map texture"}, "bump_map": {"type": "string", "description": "Path to bump map texture"}, "texture_scale": {"type": "number", "minimum": 0.001, "maximum": 100.0, "default": 1.0, "description": "Texture coordinate scale"}}}, "modifier": {"type": "object", "required": ["type", "name"], "properties": {"type": {"type": "string", "enum": ["array", "mirror", "solidify", "bevel", "subdivision_surface", "displacement", "wave", "screw"], "description": "Type of modifier"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Name of the modifier"}, "enabled": {"type": "boolean", "default": true, "description": "Whether the modifier is enabled"}}, "allOf": [{"if": {"properties": {"type": {"const": "array"}}}, "then": {"properties": {"count": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 2, "description": "Number of array copies"}, "offset": {"$ref": "#/definitions/vector3", "description": "Offset between array copies"}}}}, {"if": {"properties": {"type": {"const": "mirror"}}}, "then": {"properties": {"axis": {"type": "array", "items": {"type": "string", "enum": ["x", "y", "z"]}, "minItems": 1, "maxItems": 3, "description": "Mirror axes"}, "merge": {"type": "boolean", "default": true, "description": "Merge vertices at mirror plane"}}}}, {"if": {"properties": {"type": {"const": "solidify"}}}, "then": {"properties": {"thickness": {"type": "number", "minimum": 0.001, "maximum": 10.0, "default": 0.1, "description": "Solidify thickness"}, "offset": {"type": "number", "minimum": -1.0, "maximum": 1.0, "default": 0.0, "description": "Solidify offset"}}}}]}, "object": {"type": "object", "required": ["id", "name", "geometry"], "properties": {"id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "minLength": 1, "maxLength": 50, "description": "Unique identifier for the object"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable name for the object"}, "geometry": {"$ref": "#/definitions/geometry", "description": "Geometric properties of the object"}, "transform": {"$ref": "#/definitions/transform", "description": "Transformation properties (position, rotation, scale)"}, "material": {"$ref": "#/definitions/material", "description": "Material properties of the object"}, "modifiers": {"type": "array", "items": {"$ref": "#/definitions/modifier"}, "description": "Array of modifiers applied to the object"}, "visible": {"type": "boolean", "default": true, "description": "Whether the object is visible"}, "parent_id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "description": "ID of parent object for hierarchical relationships"}, "physics": {"type": "object", "properties": {"type": {"type": "string", "enum": ["none", "static", "dynamic", "kinematic"], "default": "none", "description": "Physics body type"}, "mass": {"type": "number", "minimum": 0.001, "maximum": 1000.0, "default": 1.0, "description": "Object mass for physics simulation"}, "friction": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 0.5, "description": "Surface friction coefficient"}, "restitution": {"type": "number", "minimum": 0.0, "maximum": 1.0, "default": 0.0, "description": "Bounce/restitution coefficient"}}}, "animation": {"type": "object", "properties": {"keyframes": {"type": "array", "items": {"type": "object", "required": ["frame", "property", "value"], "properties": {"frame": {"type": "integer", "minimum": 1, "description": "Animation frame number"}, "property": {"type": "string", "enum": ["location", "rotation", "scale"], "description": "Animated property"}, "value": {"$ref": "#/definitions/vector3", "description": "Property value at this frame"}, "interpolation": {"type": "string", "enum": ["linear", "bezier", "constant"], "default": "linear", "description": "Interpolation method"}}}}}}}}, "group": {"type": "object", "required": ["id", "name", "object_ids"], "properties": {"id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "minLength": 1, "maxLength": 50, "description": "Unique identifier for the group"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable name for the group"}, "object_ids": {"type": "array", "items": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"}, "minItems": 1, "description": "Array of object IDs in this group"}, "transform": {"$ref": "#/definitions/transform", "description": "Group-level transformation"}, "visible": {"type": "boolean", "default": true, "description": "Whether the group is visible"}}}, "mcp_structure": {"type": "object", "required": ["id", "name", "structure_type"], "properties": {"id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "minLength": 1, "maxLength": 50, "description": "Unique identifier for the MCP structure"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable name for the MCP structure"}, "structure_type": {"type": "string", "enum": ["molecular", "protein"], "description": "Type of MCP structure"}, "transform": {"$ref": "#/definitions/transform", "description": "Structure transformation"}}, "allOf": [{"if": {"properties": {"structure_type": {"const": "molecular"}}}, "then": {"properties": {"atoms": {"type": "array", "items": {"type": "object", "required": ["id", "element", "position"], "properties": {"id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "description": "Unique atom identifier"}, "element": {"type": "string", "pattern": "^[A-Z][a-z]?$", "description": "Chemical element symbol (e.g., C, O, N, H)"}, "position": {"$ref": "#/definitions/vector3", "description": "Atom position in 3D space"}, "radius": {"type": "number", "minimum": 0.1, "maximum": 5.0, "description": "Atom radius for visualization"}, "color": {"$ref": "#/definitions/color", "description": "Atom color override"}}}, "minItems": 1, "description": "Array of atoms in the molecular structure"}, "bonds": {"type": "array", "items": {"type": "object", "required": ["atom1_id", "atom2_id", "bond_type"], "properties": {"atom1_id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "description": "ID of first atom"}, "atom2_id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "description": "ID of second atom"}, "bond_type": {"type": "string", "enum": ["single", "double", "triple", "aromatic"], "description": "Type of chemical bond"}, "length": {"type": "number", "minimum": 0.5, "maximum": 5.0, "description": "Bond length override"}}}, "description": "Array of bonds between atoms"}, "representation": {"type": "string", "enum": ["ball_and_stick", "space_filling", "wireframe", "cartoon"], "default": "ball_and_stick", "description": "Molecular representation style"}}}}, {"if": {"properties": {"structure_type": {"const": "protein"}}}, "then": {"properties": {"pdb_file": {"type": "string", "description": "Path to PDB file for protein structure"}, "chain_ids": {"type": "array", "items": {"type": "string", "pattern": "^[A-Z]$"}, "description": "Protein chain IDs to include"}, "representation": {"type": "string", "enum": ["cartoon", "ribbon", "surface", "ball_and_stick"], "default": "cartoon", "description": "Protein representation style"}, "color_scheme": {"type": "string", "enum": ["chain", "residue", "secondary_structure", "custom"], "default": "chain", "description": "Protein coloring scheme"}, "show_sidechains": {"type": "boolean", "default": false, "description": "Whether to show amino acid side chains"}, "show_backbone": {"type": "boolean", "default": true, "description": "Whether to show protein backbone"}}}}]}}}
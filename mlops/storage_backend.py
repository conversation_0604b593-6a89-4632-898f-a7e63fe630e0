"""
Storage backends for MLOps artifacts and models.

This module provides different storage backends for storing model artifacts,
experiment data, and other MLOps-related files.
"""

import shutil
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import logging

logger = logging.getLogger(__name__)


class StorageBackend(ABC):
    """Abstract base class for storage backends."""
    
    @abstractmethod
    def save_file(self, local_path: Union[str, Path], remote_path: str) -> bool:
        """Save a file to the storage backend."""
        pass
    
    @abstractmethod
    def load_file(self, remote_path: str, local_path: Union[str, Path]) -> bool:
        """Load a file from the storage backend."""
        pass
    
    @abstractmethod
    def delete_file(self, remote_path: str) -> bool:
        """Delete a file from the storage backend."""
        pass
    
    @abstractmethod
    def list_files(self, prefix: str = "") -> List[str]:
        """List files in the storage backend."""
        pass
    
    @abstractmethod
    def file_exists(self, remote_path: str) -> bool:
        """Check if a file exists in the storage backend."""
        pass
    
    @abstractmethod
    def get_file_info(self, remote_path: str) -> Optional[Dict[str, Any]]:
        """Get file metadata."""
        pass


class LocalStorageBackend(StorageBackend):
    """Local filesystem storage backend."""
    
    def __init__(self, base_path: Optional[Union[str, Path]] = None):
        """Initialize local storage backend."""
        if base_path is None:
            base_path = Path("mlops_data/storage")
        
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized local storage at {self.base_path}")
    
    def _get_full_path(self, remote_path: str) -> Path:
        """Get full local path for a remote path."""
        return self.base_path / remote_path
    
    def save_file(self, local_path: Union[str, Path], remote_path: str) -> bool:
        """Save a file to local storage."""
        try:
            local_path = Path(local_path)
            full_path = self._get_full_path(remote_path)
            
            # Create parent directories
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            if local_path.is_dir():
                if full_path.exists():
                    shutil.rmtree(full_path)
                shutil.copytree(local_path, full_path)
            else:
                shutil.copy2(local_path, full_path)
            
            logger.info(f"Saved {local_path} to {remote_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save file {local_path} to {remote_path}: {e}")
            return False
    
    def load_file(self, remote_path: str, local_path: Union[str, Path]) -> bool:
        """Load a file from local storage."""
        try:
            full_path = self._get_full_path(remote_path)
            local_path = Path(local_path)
            
            if not full_path.exists():
                logger.error(f"File {remote_path} does not exist")
                return False
            
            # Create parent directories
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file
            if full_path.is_dir():
                if local_path.exists():
                    shutil.rmtree(local_path)
                shutil.copytree(full_path, local_path)
            else:
                shutil.copy2(full_path, local_path)
            
            logger.info(f"Loaded {remote_path} to {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load file {remote_path} to {local_path}: {e}")
            return False
    
    def delete_file(self, remote_path: str) -> bool:
        """Delete a file from local storage."""
        try:
            full_path = self._get_full_path(remote_path)
            
            if not full_path.exists():
                logger.warning(f"File {remote_path} does not exist")
                return True
            
            if full_path.is_dir():
                shutil.rmtree(full_path)
            else:
                full_path.unlink()
            
            logger.info(f"Deleted {remote_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete file {remote_path}: {e}")
            return False
    
    def list_files(self, prefix: str = "") -> List[str]:
        """List files in local storage."""
        try:
            if prefix:
                search_path = self._get_full_path(prefix)
            else:
                search_path = self.base_path
            
            files = []
            
            if search_path.exists():
                if search_path.is_file():
                    # If prefix points to a file, return just that file
                    files.append(str(search_path.relative_to(self.base_path)))
                else:
                    # Recursively find all files
                    for file_path in search_path.rglob("*"):
                        if file_path.is_file():
                            rel_path = file_path.relative_to(self.base_path)
                            files.append(str(rel_path))
            
            return sorted(files)
            
        except Exception as e:
            logger.error(f"Failed to list files with prefix {prefix}: {e}")
            return []
    
    def file_exists(self, remote_path: str) -> bool:
        """Check if a file exists in local storage."""
        full_path = self._get_full_path(remote_path)
        return full_path.exists()
    
    def get_file_info(self, remote_path: str) -> Optional[Dict[str, Any]]:
        """Get file metadata."""
        try:
            full_path = self._get_full_path(remote_path)
            
            if not full_path.exists():
                return None
            
            stat = full_path.stat()
            
            return {
                'path': remote_path,
                'size': stat.st_size,
                'modified_time': stat.st_mtime,
                'is_directory': full_path.is_dir(),
                'permissions': oct(stat.st_mode)[-3:]
            }
            
        except Exception as e:
            logger.error(f"Failed to get file info for {remote_path}: {e}")
            return None
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        try:
            total_size = 0
            file_count = 0
            dir_count = 0
            
            for item in self.base_path.rglob("*"):
                if item.is_file():
                    total_size += item.stat().st_size
                    file_count += 1
                elif item.is_dir():
                    dir_count += 1
            
            return {
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'file_count': file_count,
                'directory_count': dir_count,
                'base_path': str(self.base_path)
            }
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {e}")
            return {}


class ModelArtifactManager:
    """Manages model artifacts using a storage backend."""
    
    def __init__(self, storage_backend: StorageBackend):
        """Initialize with a storage backend."""
        self.storage = storage_backend
    
    def save_model_artifact(self,
                           model_name: str,
                           version: str,
                           artifact_name: str,
                           local_path: Union[str, Path]) -> str:
        """Save a model artifact."""
        remote_path = f"models/{model_name}/{version}/{artifact_name}"
        
        if self.storage.save_file(local_path, remote_path):
            return remote_path
        else:
            raise RuntimeError(f"Failed to save artifact {artifact_name}")
    
    def load_model_artifact(self,
                           model_name: str,
                           version: str,
                           artifact_name: str,
                           local_path: Union[str, Path]) -> bool:
        """Load a model artifact."""
        remote_path = f"models/{model_name}/{version}/{artifact_name}"
        return self.storage.load_file(remote_path, local_path)
    
    def list_model_artifacts(self, model_name: str, version: str) -> List[str]:
        """List artifacts for a model version."""
        prefix = f"models/{model_name}/{version}/"
        files = self.storage.list_files(prefix)
        
        # Return just the artifact names (remove prefix)
        artifacts = []
        for file_path in files:
            if file_path.startswith(prefix):
                artifact_name = file_path[len(prefix):]
                artifacts.append(artifact_name)
        
        return artifacts
    
    def delete_model_artifacts(self, model_name: str, version: str) -> bool:
        """Delete all artifacts for a model version."""
        prefix = f"models/{model_name}/{version}/"
        files = self.storage.list_files(prefix)
        
        success = True
        for file_path in files:
            if not self.storage.delete_file(file_path):
                success = False
        
        return success
    
    def save_experiment_artifact(self,
                                experiment_id: str,
                                artifact_name: str,
                                local_path: Union[str, Path]) -> str:
        """Save an experiment artifact."""
        remote_path = f"experiments/{experiment_id}/{artifact_name}"
        
        if self.storage.save_file(local_path, remote_path):
            return remote_path
        else:
            raise RuntimeError(f"Failed to save experiment artifact {artifact_name}")
    
    def load_experiment_artifact(self,
                                experiment_id: str,
                                artifact_name: str,
                                local_path: Union[str, Path]) -> bool:
        """Load an experiment artifact."""
        remote_path = f"experiments/{experiment_id}/{artifact_name}"
        return self.storage.load_file(remote_path, local_path)

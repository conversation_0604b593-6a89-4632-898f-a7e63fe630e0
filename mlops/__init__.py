"""
MLOps module for model versioning, experiment tracking, and model registry.

This module provides comprehensive MLOps infrastructure for the Blender AI Agent system,
supporting version management and experiment tracking for:
- Computer Vision models
- LLM configurations
- RL policies
- Model artifacts and metadata
"""

from .model_registry import ModelRegistry, ModelMetadata, ModelType, ModelStatus
from .experiment_tracker import ExperimentTracker, Experiment, ExperimentMetrics
from .version_manager import ModelVersionManager, ModelVersion
from .storage_backend import StorageBackend, LocalStorageBackend, ModelArtifactManager

__all__ = [
    'ModelRegistry',
    'ModelMetadata',
    'ModelType',
    'ModelStatus',
    'ExperimentTracker',
    'Experiment',
    'ExperimentMetrics',
    'ModelVersionManager',
    'ModelVersion',
    'StorageBackend',
    'LocalStorageBackend',
    'ModelArtifactManager'
]

__version__ = "1.0.0"

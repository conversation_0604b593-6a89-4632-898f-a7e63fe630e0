"""
Model Registry for managing AI model metadata and versions.

This module provides a centralized registry for tracking all AI models
used in the Blender AI Agent system, including their metadata, versions,
and relationships.
"""

import json
import uuid
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
import hashlib
import logging

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """Types of models supported by the registry."""
    COMPUTER_VISION = "cv"
    LANGUAGE_MODEL = "llm"
    REINFORCEMENT_LEARNING = "rl"
    EMBEDDING = "embedding"
    CUSTOM = "custom"


class ModelStatus(Enum):
    """Model status in the registry."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"


@dataclass
class ModelMetadata:
    """Metadata for a registered model."""
    model_id: str
    name: str
    model_type: ModelType
    version: str
    description: str
    created_at: datetime
    updated_at: datetime
    status: ModelStatus
    tags: List[str]
    
    # Model-specific metadata
    framework: Optional[str] = None  # pytorch, tensorflow, sklearn, etc.
    architecture: Optional[str] = None  # resnet50, gpt-4, ppo, etc.
    input_shape: Optional[Dict[str, Any]] = None
    output_shape: Optional[Dict[str, Any]] = None
    
    # Training metadata
    training_dataset: Optional[str] = None
    training_parameters: Optional[Dict[str, Any]] = None
    performance_metrics: Optional[Dict[str, float]] = None
    
    # Storage metadata
    model_path: Optional[str] = None
    config_path: Optional[str] = None
    weights_path: Optional[str] = None
    checksum: Optional[str] = None
    
    # Relationships
    parent_model_id: Optional[str] = None
    experiment_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        # Convert datetime objects to ISO format
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        # Convert enums to values
        data['model_type'] = self.model_type.value
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelMetadata':
        """Create from dictionary."""
        # Convert datetime strings back to datetime objects
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        # Convert enum values back to enums
        data['model_type'] = ModelType(data['model_type'])
        data['status'] = ModelStatus(data['status'])
        return cls(**data)


class ModelRegistry:
    """Central registry for managing AI model metadata and versions."""
    
    def __init__(self, registry_path: Optional[Path] = None):
        """Initialize the model registry."""
        if registry_path is None:
            registry_path = Path("mlops_data/model_registry.json")
        
        self.registry_path = Path(registry_path)
        self.registry_path.parent.mkdir(parents=True, exist_ok=True)
        
        self._models: Dict[str, ModelMetadata] = {}
        self._load_registry()
    
    def _load_registry(self) -> None:
        """Load existing registry from disk."""
        if self.registry_path.exists():
            try:
                with open(self.registry_path, 'r') as f:
                    data = json.load(f)
                
                for model_id, model_data in data.items():
                    self._models[model_id] = ModelMetadata.from_dict(model_data)
                
                logger.info(f"Loaded {len(self._models)} models from registry")
            except Exception as e:
                logger.error(f"Failed to load registry: {e}")
                self._models = {}
    
    def _save_registry(self) -> None:
        """Save registry to disk."""
        try:
            data = {model_id: model.to_dict() for model_id, model in self._models.items()}
            
            with open(self.registry_path, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.info(f"Saved registry with {len(self._models)} models")
        except Exception as e:
            logger.error(f"Failed to save registry: {e}")
    
    def register_model(self, 
                      name: str,
                      model_type: ModelType,
                      version: str,
                      description: str,
                      **kwargs) -> str:
        """Register a new model in the registry."""
        model_id = str(uuid.uuid4())
        now = datetime.now()
        
        metadata = ModelMetadata(
            model_id=model_id,
            name=name,
            model_type=model_type,
            version=version,
            description=description,
            created_at=now,
            updated_at=now,
            status=ModelStatus.DEVELOPMENT,
            tags=[],
            **kwargs
        )
        
        self._models[model_id] = metadata
        self._save_registry()
        
        logger.info(f"Registered model {name} v{version} with ID {model_id}")
        return model_id
    
    def get_model(self, model_id: str) -> Optional[ModelMetadata]:
        """Get model metadata by ID."""
        return self._models.get(model_id)
    
    def update_model(self, model_id: str, **updates) -> bool:
        """Update model metadata."""
        if model_id not in self._models:
            return False
        
        model = self._models[model_id]
        
        # Update allowed fields
        for key, value in updates.items():
            if hasattr(model, key):
                setattr(model, key, value)
        
        model.updated_at = datetime.now()
        self._save_registry()
        
        logger.info(f"Updated model {model_id}")
        return True
    
    def list_models(self, 
                   model_type: Optional[ModelType] = None,
                   status: Optional[ModelStatus] = None,
                   tags: Optional[List[str]] = None) -> List[ModelMetadata]:
        """List models with optional filtering."""
        models = list(self._models.values())
        
        if model_type:
            models = [m for m in models if m.model_type == model_type]
        
        if status:
            models = [m for m in models if m.status == status]
        
        if tags:
            models = [m for m in models if any(tag in m.tags for tag in tags)]
        
        return sorted(models, key=lambda m: m.created_at, reverse=True)
    
    def delete_model(self, model_id: str) -> bool:
        """Delete a model from the registry."""
        if model_id in self._models:
            del self._models[model_id]
            self._save_registry()
            logger.info(f"Deleted model {model_id}")
            return True
        return False
    
    def get_model_by_name_version(self, name: str, version: str) -> Optional[ModelMetadata]:
        """Get model by name and version."""
        for model in self._models.values():
            if model.name == name and model.version == version:
                return model
        return None
    
    def calculate_checksum(self, file_path: Union[str, Path]) -> str:
        """Calculate SHA256 checksum for a file."""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def get_latest_version(self, name: str) -> Optional[ModelMetadata]:
        """Get the latest version of a model by name."""
        models = [m for m in self._models.values() if m.name == name]
        if not models:
            return None
        
        # Sort by version (assuming semantic versioning)
        return max(models, key=lambda m: m.created_at)
    
    def get_model_lineage(self, model_id: str) -> List[ModelMetadata]:
        """Get the lineage (parent chain) of a model."""
        lineage = []
        current_id = model_id
        
        while current_id:
            model = self.get_model(current_id)
            if not model:
                break
            
            lineage.append(model)
            current_id = model.parent_model_id
        
        return lineage
